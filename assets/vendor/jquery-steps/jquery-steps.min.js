/*!
    * Steps v1.0.3
    * https://github.com/oguzhanoya/jquery-steps
    *
    * Copyright (c) 2020 oguzhanoya
    * Released under the MIT license
    */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).$)}(this,function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var a=e(t);function s(t,e){for(var o=0;o<e.length;o++){var i=e[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var n={startAt:0,showBackButton:!0,showFooterButtons:!0,onInit:$.noop,onDestroy:$.noop,onFinish:$.noop,onChange:function(){return!0},stepSelector:".step-steps > li",contentSelector:".step-content > .step-tab-panel",footerSelector:".step-footer",buttonSelector:"button",activeClass:"active",doneClass:"done",errorClass:"error"},o=function(){function o(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o),this.options=a.default.extend({},n,e),this.el=a.default(t),this.init()}var t,e,i;return t=o,i=[{key:"setDefaults",value:function(t){a.default.extend(n,a.default.isPlainObject(t)&&t)}}],(e=[{key:"stepClick",value:function(t){t.preventDefault();var e=a.default(this).closest("li").index(),o=t.data.self.getStepIndex();t.data.self.setActiveStep(o,e)}},{key:"btnClick",value:function(t){t.preventDefault();var e=a.default(this).data("direction");t.data.self.setAction(e)}},{key:"init",value:function(){this.hook("onInit");a.default(this.el).find(this.options.stepSelector).on("click",{self:this},this.stepClick),a.default(this.el).find("".concat(this.options.footerSelector," ").concat(this.options.buttonSelector)).on("click",{self:this},this.btnClick),this.setShowStep(this.options.startAt,"",this.options.activeClass),this.setFooterBtns(),this.options.showFooterButtons||(this.hideFooterBtns(),this.setFooterBtns=a.default.noop)}},{key:"hook",value:function(t){void 0!==this.options[t]&&this.options[t].call(this.el)}},{key:"destroy",value:function(){a.default(this.el).find(this.options.stepSelector).off("click",this.stepClick),a.default(this.el).find("".concat(this.options.footerSelector," ").concat(this.options.buttonSelector)).off("click",this.btnClick),this.el.removeData("plugin_Steps"),this.hook("onDestroy")}},{key:"getStepIndex",value:function(){return this.el.find(this.options.stepSelector).filter(".".concat(this.options.activeClass)).index()||0}},{key:"getMaxStepCount",value:function(){return this.el.find(this.options.stepSelector).length-1}},{key:"getStepDirection",value:function(t,e){var o="none";return e<t?o="backward":t<e&&(o="forward"),o}},{key:"setShowStep",value:function(t,e,o){var i=2<arguments.length&&void 0!==o?o:"";this.el.find(this.options.contentSelector).removeClass(this.options.activeClass);var s=this.el.find(this.options.stepSelector).eq(t);s.removeClass(e).addClass(i);var n=s.find("a").attr("href");a.default(n).addClass(this.options.activeClass)}},{key:"setActiveStep",value:function(t,e){if(e!==t){if(t<e)for(var o=0;o<=e;o+=1){o===e?this.setShowStep(o,this.options.doneClass,this.options.activeClass):this.setShowStep(o,"".concat(this.options.activeClass," ").concat(this.options.errorClass),this.options.doneClass);var i=this.getStepDirection(o,e);if(!this.options.onChange(o,e,i)){this.setShowStep(o,this.options.doneClass,"".concat(this.options.activeClass," ").concat(this.options.errorClass)),this.setFooterBtns();break}}if(e<t)for(var s=t;e<=s;--s){var n=this.getStepDirection(s,e),a=this.options.onChange(s,e,n);if(this.setShowStep(s,"".concat(this.options.doneClass," ").concat(this.options.activeClass," ").concat(this.options.errorClass)),s===e&&this.setShowStep(s,"".concat(this.options.doneClass," ").concat(this.options.errorClass),this.options.activeClass),!a){this.setShowStep(s,this.options.doneClass,"".concat(this.options.activeClass," ").concat(this.options.errorClass)),this.setFooterBtns();break}}this.setFooterBtns()}}},{key:"setFooterBtns",value:function(){var t=this.getStepIndex(),e=this.getMaxStepCount(),o=this.el.find(this.options.footerSelector);0===t&&o.find('button[data-direction="prev"]').hide(),0<t&&this.options.showBackButton&&o.find('button[data-direction="prev"]').show(),e===t?(o.find('button[data-direction="prev"]').show(),o.find('button[data-direction="next"]').hide(),o.find('button[data-direction="finish"]').show()):(o.find('button[data-direction="next"]').show(),o.find('button[data-direction="finish"]').hide()),this.options.showBackButton||o.find('button[data-direction="prev"]').hide()}},{key:"setAction",value:function(t){var e=this.getStepIndex(),o=e;"prev"===t&&--o,"next"===t&&(o+=1),"finish"===t&&(this.options.onChange(e,o,"forward")?this.hook("onFinish"):this.setShowStep(e,"","error")),"finish"!==t&&this.setActiveStep(e,o)}},{key:"next",value:function(){var t=this.getStepIndex();return this.getMaxStepCount()===t?this.setAction("finish"):this.setAction("next")}},{key:"prev",value:function(){return 0!==this.getStepIndex()&&this.setAction("prev")}},{key:"finish",value:function(){this.hook("onFinish")}},{key:"hideFooterBtns",value:function(){this.el.find(this.options.footerSelector).hide()}}])&&s(t.prototype,e),i&&s(t,i),o}(),i=a.default.fn.steps;a.default.fn.steps=function(t){return this.each(function(){a.default.data(this,"plugin_Steps")||a.default.data(this,"plugin_Steps",new o(this,t))})},a.default.fn.steps.version="1.0.2",a.default.fn.steps.setDefaults=o.setDefaults,a.default.fn.steps.noConflict=function(){return a.default.fn.steps=i,this}});