<?php
// Cache test file - delete this after testing
echo "Cache test - Current time: " . date('Y-m-d H:i:s') . "<br>";
echo "If you see the same time after making changes, cache is active.<br>";
echo "Random number: " . rand(1000, 9999) . "<br>";

// Test if our controller changes are loaded
if (class_exists('App\Controller\Front\GuestCheckoutController')) {
    echo "GuestCheckoutController class exists<br>";
} else {
    echo "GuestCheckoutController class NOT found<br>";
}

// Clear OpCache if available
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "OpCache cleared!<br>";
} else {
    echo "OpCache not available<br>";
}

// Show PHP cache settings
echo "OpCache enabled: " . (ini_get('opcache.enable') ? 'Yes' : 'No') . "<br>";
echo "OpCache validate timestamps: " . (ini_get('opcache.validate_timestamps') ? 'Yes' : 'No') . "<br>";
?>
