version: '3'

services:
  mysql:
    image: mysql:5.7
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: eventic
      MYSQL_USER: eventic
      MYSQL_PASSWORD: eventic123
      MYSQL_SQL_MODE: ''
    volumes:
      - mysql_data:/var/lib/mysql
    command: --sql-mode=""

  php:
    image: php:7.4-apache
    volumes:
      - .:/var/www/html
      - ./docker/apache.conf:/etc/apache2/sites-available/000-default.conf
    ports:
      - "8000:80"
    environment:
      - APP_ENV=dev
      - APP_DEBUG=1
    depends_on:
      - mysql
    command: |
      bash -c 'a2enmod rewrite &&
               apt-get update &&
               apt-get install -y libzip-dev zip unzip git libicu-dev libxml2-dev libpng-dev libjpeg-dev libfreetype6-dev &&
               docker-php-ext-install pdo_mysql mysqli zip intl opcache xml gd &&
               docker-php-ext-configure gd --with-freetype --with-jpeg &&
               docker-php-ext-install -j$$(nproc) gd &&
               chown -R www-data:www-data /var/www/html &&
               apache2-foreground'

volumes:
  mysql_data:
