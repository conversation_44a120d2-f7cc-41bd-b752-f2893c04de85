{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "bc733a51d1e21a1f8148db52357994e2", "packages": [{"name": "a2lix/auto-form-bundle", "version": "0.3", "source": {"type": "git", "url": "https://github.com/a2lix/AutoFormBundle.git", "reference": "a22365315c704bcc5358ddeecd66b9668de4eea4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/a2lix/AutoFormBundle/zipball/a22365315c704bcc5358ddeecd66b9668de4eea4", "reference": "a22365315c704bcc5358ddeecd66b9668de4eea4", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/doctrine-bridge": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "require-dev": {"doctrine/orm": "^2.4", "friendsofphp/php-cs-fixer": "^2.10", "symfony/phpunit-bridge": "^4.0", "symfony/validator": "^3.4|^4.0", "vimeo/psalm": "^1.0"}, "suggest": {"a2lix/translation-form-bundle": "For translation form"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "0.x-dev"}}, "autoload": {"psr-4": {"A2lix\\AutoFormBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "David <PERSON>", "homepage": "http://a2lix.fr"}, {"name": "Contributors", "homepage": "https://github.com/a2lix/AutoFormBundle/contributors"}], "description": "Automate form building", "homepage": "https://github.com/a2lix/AutoFormBundle", "keywords": ["automate", "automation", "building", "field", "form", "magic", "symfony"], "time": "2019-12-02T19:07:13+00:00"}, {"name": "a2lix/translation-form-bundle", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/a2lix/TranslationFormBundle.git", "reference": "b806d72585c9832e4eb3f67cd70b5ac548edfa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/a2lix/TranslationFormBundle/zipball/b806d72585c9832e4eb3f67cd70b5ac548edfa90", "reference": "b806d72585c9832e4eb3f67cd70b5ac548edfa90", "shasum": ""}, "require": {"a2lix/auto-form-bundle": "^0.2|^0.3", "php": "^7.1.3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/doctrine-bridge": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0", "symfony/options-resolver": "^3.4|^4.0|^5.0"}, "require-dev": {"doctrine/orm": "^2.4", "friendsofphp/php-cs-fixer": "^2.10", "knplabs/doctrine-behaviors": "^1.4", "matthiasnoback/symfony-dependency-injection-test": "^3.0", "symfony/phpunit-bridge": "^4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "vimeo/psalm": "^1.0"}, "suggest": {"knplabs/doctrine-behaviors": "For Knp strategy", "prezent/doctrine-translatable-bundle": "For Prezent strategy"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"A2lix\\TranslationFormBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "David <PERSON>", "homepage": "http://a2lix.fr"}, {"name": "Contributors", "homepage": "https://github.com/a2lix/TranslationFormBundle/contributors"}], "description": "Translate your doctrine objects easily with some helps", "homepage": "https://github.com/a2lix/TranslationFormBundle", "keywords": ["doctrine2", "form", "i18n", "internationalization", "knplabs", "prezent", "symfony", "translatable", "translation"], "time": "2019-12-10T19:06:38+00:00"}, {"name": "beberlei/doctrineextensions", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/beberlei/DoctrineExtensions.git", "reference": "af72c4a136b744f1268ca8bb4da47a2f8af78f86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/DoctrineExtensions/zipball/af72c4a136b744f1268ca8bb4da47a2f8af78f86", "reference": "af72c4a136b744f1268ca8bb4da47a2f8af78f86", "shasum": ""}, "require": {"doctrine/orm": "^2.6", "php": "^7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "nesbot/carbon": "*", "phpunit/phpunit": "^7.0 || ^8.0", "symfony/yaml": "^4.2", "zf1/zend-date": "^1.12", "zf1/zend-registry": "^1.12"}, "type": "library", "autoload": {"psr-4": {"DoctrineExtensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A set of extensions to Doctrine 2 that add support for additional query functions available in MySQL and Oracle.", "keywords": ["database", "doctrine", "orm"], "time": "2019-12-05T09:49:04+00:00"}, {"name": "behat/transliterator", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^4.8.36|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "time": "2020-01-14T16:39:13+00:00"}, {"name": "clue/stream-filter", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "5a58cc30a8bd6a4eb8f856adf61dd3e013f53f71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/5a58cc30a8bd6a4eb8f856adf61dd3e013f53f71", "reference": "5a58cc30a8bd6a4eb8f856adf61dd3e013f53f71", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8"}, "type": "library", "autoload": {"psr-4": {"Clue\\StreamFilter\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "time": "2019-04-09T12:31:48+00:00"}, {"name": "cmen/google-charts-bundle", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/cmen/CMENGoogleChartsBundle.git", "reference": "da6baee249d841407bdfecee69a94402ae50afd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cmen/CMENGoogleChartsBundle/zipball/da6baee249d841407bdfecee69a94402ae50afd2", "reference": "da6baee249d841407bdfecee69a94402ae50afd2", "shasum": ""}, "require": {"ext-json": "*", "php": "^5.6 || ^7.0", "symfony/framework-bundle": "^2.7 || ^3.0 || ^4.0 || ^5.0", "twig/twig": "^1.42 || ^2.12 || ^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^5.7"}, "type": "symfony-bundle", "autoload": {"psr-4": {"CMEN\\GoogleChartsBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "This Bundle provides a Twig extension and PHP objects to display Google charts in your Symfony application.", "keywords": ["chart", "charts", "google", "graph", "graphs", "map"], "time": "2019-12-06T16:29:20+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "95c63ab2117a72f48f5a55da9740a3273d45b7fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/95c63ab2117a72f48f5a55da9740a3273d45b7fd", "reference": "95c63ab2117a72f48f5a55da9740a3273d45b7fd", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2020-04-08T08:27:21+00:00"}, {"name": "coresphere/console-bundle", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/CoreSphere/ConsoleBundle.git", "reference": "06eb5965db81bf556f887f661ecd03719cd0691c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CoreSphere/ConsoleBundle/zipball/06eb5965db81bf556f887f661ecd03719cd0691c", "reference": "06eb5965db81bf556f887f661ecd03719cd0691c", "shasum": ""}, "require": {"php": "^5.5|^7.0", "symfony/console": "~2.7|~3.0|~4.0", "symfony/dependency-injection": "~2.7|~3.0|~4.0", "symfony/event-dispatcher": "~2.7|~3.0|~4.0", "symfony/framework-bundle": "~2.7|~3.0|~4.0", "symfony/http-kernel": "~2.7|~3.0|~4.0"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^2.2", "doctrine/doctrine-migrations-bundle": "^1.0", "phpunit/phpunit": "^4.8", "symfony/finder": "^2.7|~4.0", "symfony/templating": "^3.2|~4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"CoreSphere\\ConsoleBundle\\": ""}, "exclude-from-classmap": ["Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle allows you accessing the symfony2 console via your browser", "keywords": ["console", "javascript", "symfony"], "time": "2017-12-11T15:37:10+00:00"}, {"name": "debril/feed-io", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/alexdebril/feed-io.git", "reference": "7f032bf0d91d4a0ea670e160070e66efb01f1d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alexdebril/feed-io/zipball/7f032bf0d91d4a0ea670e160070e66efb01f1d71", "reference": "7f032bf0d91d4a0ea670e160070e66efb01f1d71", "shasum": ""}, "require": {"ext-dom": "*", "guzzlehttp/guzzle": "~6.2", "php": ">=7.1", "psr/log": "~1.0", "symfony/console": "~3.4|~4.0|~5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.4", "monolog/monolog": "1.*", "phpunit/phpunit": "~7.5.0"}, "suggest": {"monolog/monolog": "Allows to handle logs"}, "bin": ["bin/feedio"], "type": "library", "autoload": {"psr-4": {"FeedIo\\": "src/FeedIo"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP library built to consume and serve JSONFeed / RSS / Atom feeds", "homepage": "https://feed-io.net", "keywords": ["atom", "cli", "client", "feed", "jsonfeed", "news", "rss"], "time": "2020-05-25T13:12:32+00:00"}, {"name": "debril/rss-atom-bundle", "version": "v5.0.5", "target-dir": "Debril/RssAtomBundle", "source": {"type": "git", "url": "https://github.com/alexdebril/rss-atom-bundle.git", "reference": "2c02ab52af4f993f583496b5077455de0db82408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alexdebril/rss-atom-bundle/zipball/2c02ab52af4f993f583496b5077455de0db82408", "reference": "2c02ab52af4f993f583496b5077455de0db82408", "shasum": ""}, "require": {"debril/feed-io": "~3.0|~4.0", "php": ">=7.1", "symfony/config": "~3.4|~4.0|^5.0", "symfony/dependency-injection": "~3.4|~4.0|^5.0", "symfony/http-foundation": "~3.4|~4.0|^5.0", "symfony/http-kernel": "~3.4|~4.0|^5.0"}, "require-dev": {"doctrine/common": "~2.3", "phpunit/phpunit": "~7.0", "symfony/browser-kit": ">3.0", "symfony/finder": ">3.0", "symfony/framework-bundle": "^4.3|^5.0", "symfony/validator": ">3.0", "symfony/yaml": "^4.0|^5.0"}, "type": "symfony-bundle", "autoload": {"psr-0": {"Debril\\RssAtomBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "RSS / Atom and JSONFeed support for Symfony", "homepage": "http://debril.org/category/rss-atom-bundle/", "keywords": ["Syndication", "atom", "feed", "jsonfeed", "rss"], "time": "2020-04-21T13:47:40+00:00"}, {"name": "defuse/php-encryption", "version": "v2.2.1", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "0f407c43b953d571421e0020ba92082ed5fb7620"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/0f407c43b953d571421e0020ba92082ed5fb7620", "reference": "0f407c43b953d571421e0020ba92082ed5fb7620", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.4.0"}, "require-dev": {"nikic/php-parser": "^2.0|^3.0|^4.0", "phpunit/phpunit": "^4|^5"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "time": "2018-07-24T23:27:56+00:00"}, {"name": "doctrine/annotations", "version": "1.10.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "5db60a4969eba0e0c197a19c077780aadbc43c5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/5db60a4969eba0e0c197a19c077780aadbc43c5d", "reference": "5db60a4969eba0e0c197a19c077780aadbc43c5d", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2020-05-25T17:24:27+00:00"}, {"name": "doctrine/cache", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/382e7f4db9a12dc6c19431743a2b096041bcdd62", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62", "shasum": ""}, "require": {"php": "~7.1"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "time": "2019-11-29T15:36:20+00:00"}, {"name": "doctrine/collections", "version": "1.6.5", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "fc0206348e17e530d09463fef07ba8968406cd6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/fc0206348e17e530d09463fef07ba8968406cd6d", "reference": "fc0206348e17e530d09463fef07ba8968406cd6d", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.8.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2020-05-25T19:24:35+00:00"}, {"name": "doctrine/common", "version": "2.13.1", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "fb00cd761126b11d8f334c09cf5b1f3f83fefc17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/fb00cd761126b11d8f334c09cf5b1f3f83fefc17", "reference": "fb00cd761126b11d8f334c09cf5b1f3f83fefc17", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "time": "2020-05-25T20:05:47+00:00"}, {"name": "doctrine/dbal", "version": "v2.9.3", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "7345cd59edfa2036eb0fa4264b77ae2576842035"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/7345cd59edfa2036eb0fa4264b77ae2576842035", "reference": "7345cd59edfa2036eb0fa4264b77ae2576842035", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^5.0", "jetbrains/phpstorm-stubs": "^2018.1.2", "phpstan/phpstan": "^0.10.1", "phpunit/phpunit": "^7.4", "symfony/console": "^2.0.5|^3.0|^4.0", "symfony/phpunit-bridge": "^3.4.5|^4.0.5"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.9.x-dev", "dev-develop": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "dbal", "mysql", "persistence", "pgsql", "php", "queryobject"], "time": "2019-11-02T22:19:34+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "2.0.9", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "41c94393a85bb26d580d10e24d5ab27b037dc720"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/41c94393a85bb26d580d10e24d5ab27b037dc720", "reference": "41c94393a85bb26d580d10e24d5ab27b037dc720", "shasum": ""}, "require": {"doctrine/dbal": "^2.9.0", "doctrine/persistence": "^1.3.3", "jdorn/sql-formatter": "^1.2.16", "php": "^7.1", "symfony/cache": "^4.3.3|^5.0", "symfony/config": "^4.3.3|^5.0", "symfony/console": "^3.4.30|^4.3.3|^5.0", "symfony/dependency-injection": "^4.3.3|^5.0", "symfony/doctrine-bridge": "^4.3.7|^5.0", "symfony/framework-bundle": "^3.4.30|^4.3.3|^5.0", "symfony/service-contracts": "^1.1.1|^2.0"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ocramius/proxy-manager": "^2.1", "phpunit/phpunit": "^7.5", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^4.3.3|^5.0", "symfony/proxy-manager-bridge": "^3.4|^4.3.3|^5.0", "symfony/twig-bridge": "^3.4.30|^4.3.3|^5.0", "symfony/validator": "^3.4.30|^4.3.3|^5.0", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3|^5.0", "symfony/yaml": "^3.4.30|^4.3.3|^5.0", "twig/twig": "^1.34|^2.12"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "time": "2020-05-25T12:25:06+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "856437e8de96a70233e1f0cc2352fc8dd15a899d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/856437e8de96a70233e1f0cc2352fc8dd15a899d", "reference": "856437e8de96a70233e1f0cc2352fc8dd15a899d", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "~1.0|~2.0", "doctrine/migrations": "^2.2", "php": "^7.1", "symfony/framework-bundle": "~3.4|~4.0|~5.0"}, "require-dev": {"doctrine/coding-standard": "^5.0", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-strict-rules": "^0.9", "phpunit/phpunit": "^6.4|^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "time": "2019-11-13T12:57:41+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "629572819973f13486371cb611386eb17851e85c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/629572819973f13486371cb611386eb17851e85c", "reference": "629572819973f13486371cb611386eb17851e85c", "shasum": ""}, "require": {"php": "^7.1"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "time": "2019-11-10T09:48:07+00:00"}, {"name": "doctrine/inflector", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2019-10-30T19:59:35+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/ae466f726242e637cebdd526a7d991b9433bacf1", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2019-10-21T16:45:58+00:00"}, {"name": "doctrine/lexer", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/1febd6c3ef84253d7c815bed85fc622ad207a9f8", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-06-08T11:03:04+00:00"}, {"name": "doctrine/migrations", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "a3987131febeb0e9acb3c47ab0df0af004588934"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/a3987131febeb0e9acb3c47ab0df0af004588934", "reference": "a3987131febeb0e9acb3c47ab0df0af004588934", "shasum": ""}, "require": {"doctrine/dbal": "^2.9", "ocramius/package-versions": "^1.3", "ocramius/proxy-manager": "^2.0.2", "php": "^7.1", "symfony/console": "^3.4||^4.0||^5.0", "symfony/stopwatch": "^3.4||^4.0||^5.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ext-pdo_sqlite": "*", "jdorn/sql-formatter": "^1.1", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^0.10", "phpstan/phpstan-deprecation-rules": "^0.10", "phpstan/phpstan-phpunit": "^0.10", "phpstan/phpstan-strict-rules": "^0.10", "phpunit/phpunit": "^7.0", "symfony/process": "^3.4||^4.0||^5.0", "symfony/yaml": "^3.4||^4.0||^5.0"}, "suggest": {"jdorn/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Migrations\\": "lib/Doctrine/Migrations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations", "php"], "time": "2019-12-04T06:09:14+00:00"}, {"name": "doctrine/orm", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "dafe298ce5d0b995ebe1746670704c0a35868a6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/dafe298ce5d0b995ebe1746670704c0a35868a6a", "reference": "dafe298ce5d0b995ebe1746670704c0a35868a6a", "shasum": ""}, "require": {"doctrine/annotations": "^1.8", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^2.11", "doctrine/dbal": "^2.9.3", "doctrine/event-manager": "^1.1", "doctrine/instantiator": "^1.3", "doctrine/persistence": "^1.2", "ext-pdo": "*", "ocramius/package-versions": "^1.2", "php": "^7.1", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^5.0", "phpunit/phpunit": "^7.5", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "time": "2020-03-19T06:41:02+00:00"}, {"name": "doctrine/persistence", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "time": "2020-03-21T15:13:52+00:00"}, {"name": "doctrine/reflection", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "55e71912dfcd824b2fdd16f2d9afe15684cfce79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/55e71912dfcd824b2fdd16f2d9afe15684cfce79", "reference": "55e71912dfcd824b2fdd16f2d9afe15684cfce79", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^5.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0", "phpstan/phpstan-phpunit": "^0.11.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "abandoned": "roave/better-reflection", "time": "2020-03-27T11:06:43+00:00"}, {"name": "dompdf/dompdf", "version": "v0.8.5", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "6782abfc090b132134cd6cea0ec6d76f0fce2c56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/6782abfc090b132134cd6cea0ec6d76f0fce2c56", "reference": "6782abfc090b132134cd6cea0ec6d76f0fce2c56", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.1", "phenx/php-svg-lib": "^0.3.3", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "time": "2020-02-20T03:52:51+00:00"}, {"name": "drewm/mailchimp-api", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/drewm/mailchimp-api.git", "reference": "c6cdfab4ca6ddbc3b260913470bd0a4a5cb84c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drewm/mailchimp-api/zipball/c6cdfab4ca6ddbc3b260913470bd0a4a5cb84c7a", "reference": "c6cdfab4ca6ddbc3b260913470bd0a4a5cb84c7a", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "7.0.*", "vlucas/phpdotenv": "^2.0"}, "type": "library", "autoload": {"psr-4": {"DrewM\\MailChimp\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://allinthehead.com/"}], "description": "Super-simple, minimum abstraction MailChimp API v3 wrapper", "homepage": "https://github.com/drewm/mailchimp-api", "time": "2019-08-06T09:24:58+00:00"}, {"name": "egulias/email-validator", "version": "2.1.17", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ade6887fd9bd74177769645ab5c474824f8a418a", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-02-13T22:36:52+00:00"}, {"name": "excelwebzone/recaptcha-bundle", "version": "v1.5.17", "source": {"type": "git", "url": "https://github.com/excelwebzone/EWZRecaptchaBundle.git", "reference": "ac970db25ceeb2e0334ecdf07d3e1ddc7aadba07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/excelwebzone/EWZRecaptchaBundle/zipball/ac970db25ceeb2e0334ecdf07d3e1ddc7aadba07", "reference": "ac970db25ceeb2e0334ecdf07d3e1ddc7aadba07", "shasum": ""}, "require": {"google/recaptcha": "^1.1", "php": ">=5.6 || ^7.0", "symfony/form": "^2.8 || ^3.0 || ^4.0 || ^5.0", "symfony/framework-bundle": "^2.8 || ^3.0 || ^4.0 || ^5.0", "symfony/security-bundle": "^2.8 || ^3.0 || ^4.0 || ^5.0", "symfony/validator": "^2.8 || ^3.0 || ^4.0 || ^5.0", "twig/twig": "^1.40 || ^2.9 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^5 || ^6 || ^7"}, "type": "symfony-bundle", "extra": {"symfony": {"allow-contrib": "true"}}, "autoload": {"psr-4": {"EWZ\\Bundle\\RecaptchaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://excelwebzone.com/"}], "description": "This bundle provides easy reCAPTCHA form field integration", "homepage": "https://github.com/excelwebzone/EWZRecaptchaBundle", "keywords": ["recaptcha"], "time": "2020-02-23T20:23:32+00:00"}, {"name": "exercise/htmlpurifier-bundle", "version": "v3.0", "source": {"type": "git", "url": "https://github.com/Exercise/HTMLPurifierBundle.git", "reference": "b6c238e9dce45aa8fcb9d1bac3fe3acc0999e489"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Exercise/HTMLPurifierBundle/zipball/b6c238e9dce45aa8fcb9d1bac3fe3acc0999e489", "reference": "b6c238e9dce45aa8fcb9d1bac3fe3acc0999e489", "shasum": ""}, "require": {"ezyang/htmlpurifier": "~4.0", "php": "^7.1.3", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.1 || ^4.0.1 || ^5.0", "symfony/http-kernel": "~3.4.1 || ^4.0.1 || ^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "symfony/form": "~3.4.1 || ^4.0.1 || ^5.0", "symfony/phpunit-bridge": "4.4.*", "twig/twig": "^1.35.0 || ^2.4.4 || ^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Exercise\\HTMLPurifierBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "contributors", "homepage": "https://github.com/Exercise/HTMLPurifierBundle/contributors"}], "description": "HTMLPurifier integration for your Symfony project", "homepage": "https://github.com/Exercise/HTMLPurifierBundle", "keywords": ["Purifier", "html", "htmlpurifier", "symfony"], "time": "2020-03-10T19:35:42+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.12.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "a617e55bc62a87eec73bd456d146d134ad716f03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/a617e55bc62a87eec73bd456d146d134ad716f03", "reference": "a617e55bc62a87eec73bd456d146d134ad716f03", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2019-10-28T03:44:26+00:00"}, {"name": "friendsofsymfony/comment-bundle", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSCommentBundle.git", "reference": "d1dfb218b81577b3080aa0e76f70cf8f363ae16a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSCommentBundle/zipball/d1dfb218b81577b3080aa0e76f70cf8f363ae16a", "reference": "d1dfb218b81577b3080aa0e76f70cf8f363ae16a", "shasum": ""}, "require": {"doctrine/annotations": "~1.0", "friendsofsymfony/rest-bundle": "~1.6|~2.0", "jms/serializer-bundle": "~2.0", "php": "^5.5.9|~7.0", "phpoption/phpoption": "~1.1", "symfony/asset": "~2.7|~3.0|^4.0", "symfony/dependency-injection": "~2.7|~3.0|^4.0", "symfony/form": "~2.7|~3.0|^4.0", "symfony/framework-bundle": "~2.7|~3.0|^4.0", "symfony/routing": "~2.7|~3.0|^4.0", "symfony/security-bundle": "~2.7|~3.0|^4.0", "symfony/twig-bridge": "^2.7|~3.0|^4.0", "symfony/twig-bundle": "~2.7|~3.0|^4.0", "symfony/validator": "~2.7|~3.0|^4.0", "symfony/yaml": "~2.7|~3.0|^4.0", "twig/twig": "~1.28|~2.0"}, "conflict": {"twig/twig": "<1.28"}, "require-dev": {"doctrine/doctrine-bundle": "~1.6", "doctrine/orm": "~2.3", "friendsofphp/php-cs-fixer": "^2.2", "friendsofsymfony/user-bundle": "~2.0", "ornicar/akismet-bundle": "dev-master", "phpunit/phpunit": "^4.8.35|^5.7.11|^6.5", "symfony/assetic-bundle": "~2.7", "symfony/browser-kit": "~2.7|~3.0|^4.0", "symfony/css-selector": "~2.7|~3.0|^4.0", "symfony/dom-crawler": "~2.7|~3.0|^4.0", "symfony/expression-language": "~2.7|~3.0|^4.0", "symfony/phpunit-bridge": "~2.7|~3.0|^4.0"}, "suggest": {"friendsofsymfony/user-bundle": "Allows for user integration.", "ornicar/akismet-bundle": "Integrate Akismet for spam detection."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"FOS\\CommentBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSCommentBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "This Bundle provides threaded comment functionality for Symfony2 applications", "homepage": "http://friendsofsymfony.github.com", "keywords": ["comment", "forum", "threads"], "time": "2018-03-04T14:16:43+00:00"}, {"name": "friendsofsymfony/jsrouting-bundle", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle.git", "reference": "9deaf916760ce1d64cf46460473260b02751cee5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSJsRoutingBundle/zipball/9deaf916760ce1d64cf46460473260b02751cee5", "reference": "9deaf916760ce1d64cf46460473260b02751cee5", "shasum": ""}, "require": {"php": "^7.1", "symfony/console": "~3.3|^4.0|^5.0", "symfony/framework-bundle": "~3.3|^4.0|^5.0", "symfony/serializer": "~3.3|^4.0|^5.0", "willdurand/jsonp-callback-validator": "~1.0"}, "require-dev": {"symfony/expression-language": "~3.3|^4.0|^5.0", "symfony/phpunit-bridge": "^3.3|^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"FOS\\JsRoutingBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSJsRoutingBundle/contributors"}], "description": "A pretty nice way to expose your Symfony2 routing to client applications.", "homepage": "http://friendsofsymfony.github.com", "keywords": ["Js Routing", "javascript", "routing"], "time": "2020-05-20T09:38:45+00:00"}, {"name": "friendsofsymfony/rest-bundle", "version": "2.7.4", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSRestBundle.git", "reference": "3d8501dbdfa48811ef942f5f93c358c80d5ad8eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSRestBundle/zipball/3d8501dbdfa48811ef942f5f93c358c80d5ad8eb", "reference": "3d8501dbdfa48811ef942f5f93c358c80d5ad8eb", "shasum": ""}, "require": {"doctrine/inflector": "^1.0", "php": "^7.1", "psr/log": "^1.0", "symfony/config": "^3.4|^4.3", "symfony/debug": "^3.4|^4.3", "symfony/dependency-injection": "^3.4|^4.3", "symfony/event-dispatcher": "^3.4|^4.3", "symfony/finder": "^3.4|^4.3", "symfony/framework-bundle": "^3.4|^4.3", "symfony/http-foundation": "^3.4|^4.3", "symfony/http-kernel": "^3.4|^4.3", "symfony/routing": "^3.4|^4.3", "symfony/security-core": "^3.4|^4.3", "willdurand/jsonp-callback-validator": "^1.0", "willdurand/negotiation": "^2.0"}, "conflict": {"jms/serializer": "<1.13.0", "jms/serializer-bundle": "<2.0.0", "sensio/framework-extra-bundle": "<3.0.13"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "jms/serializer": "^1.13|^2.0|^3.0", "jms/serializer-bundle": "^2.3.1|^3.0", "phpoption/phpoption": "^1.1", "psr/http-message": "^1.0", "sensio/framework-extra-bundle": "^3.0.13|^4.0|^5.0", "symfony/asset": "^3.4|^4.3", "symfony/browser-kit": "^3.4|^4.3", "symfony/css-selector": "^3.4|^4.3", "symfony/expression-language": "^3.4|^4.3", "symfony/form": "^3.4|^4.3", "symfony/phpunit-bridge": "^4.1.8", "symfony/security-bundle": "^3.4|^4.3", "symfony/serializer": "^3.4|^4.3", "symfony/templating": "^3.4|^4.3", "symfony/twig-bundle": "^3.4|^4.3", "symfony/validator": "^3.4|^4.3", "symfony/web-profiler-bundle": "^3.4|^4.3", "symfony/yaml": "^3.4|^4.3"}, "suggest": {"jms/serializer-bundle": "Add support for advanced serialization capabilities, recommended, requires ^2.0|^3.0", "sensio/framework-extra-bundle": "Add support for the request body converter and the view response listener, requires ^3.0", "symfony/expression-language": "Add support for using the expression language in the routing, requires ^2.7|^3.0", "symfony/serializer": "Add support for basic serialization capabilities and xml decoding, requires ^2.7|^3.0", "symfony/validator": "Add support for validation capabilities in the ParamFetcher, requires ^2.7|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"FOS\\RestBundle\\": ""}, "exclude-from-classmap": ["Resources/", "Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSRestBundle/contributors"}], "description": "This Bundle provides various tools to rapidly develop RESTful API's with Symfony", "homepage": "http://friendsofsymfony.github.com", "keywords": ["rest"], "time": "2020-04-23T17:34:09+00:00"}, {"name": "friendsofsymfony/user-bundle", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSUserBundle.git", "reference": "1049935edd24ec305cc6cfde1875372fa9600446"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSUserBundle/zipball/1049935edd24ec305cc6cfde1875372fa9600446", "reference": "1049935edd24ec305cc6cfde1875372fa9600446", "shasum": ""}, "require": {"paragonie/random_compat": "^1 || ^2", "php": "^5.5.9 || ^7.0", "symfony/form": "^2.8 || ^3.0 || ^4.0", "symfony/framework-bundle": "^2.8 || ^3.0 || ^4.0", "symfony/security-bundle": "^2.8 || ^3.0 || ^4.0", "symfony/templating": "^2.8 || ^3.0 || ^4.0", "symfony/twig-bundle": "^2.8 || ^3.0 || ^4.0", "symfony/validator": "^2.8 || ^3.0 || ^4.0", "twig/twig": "^1.28 || ^2.0"}, "conflict": {"doctrine/doctrine-bundle": "<1.3", "symfony/doctrine-bridge": "<2.7"}, "require-dev": {"doctrine/doctrine-bundle": "^1.3", "friendsofphp/php-cs-fixer": "^2.2", "phpunit/phpunit": "^4.8.35|^5.7.11|^6.5", "swiftmailer/swiftmailer": "^4.3 || ^5.0 || ^6.0", "symfony/console": "^2.8 || ^3.0 || ^4.0", "symfony/phpunit-bridge": "^2.8 || ^3.0 || ^4.0", "symfony/yaml": "^2.8 || ^3.0 || ^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"FOS\\UserBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSUserBundle/contributors"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Symfony FOSUserBundle", "homepage": "http://friendsofsymfony.github.com", "keywords": ["User management"], "time": "2018-03-08T08:59:27+00:00"}, {"name": "gedmo/doctrine-extensions", "version": "v2.4.41", "source": {"type": "git", "url": "https://github.com/Atlantic18/DoctrineExtensions.git", "reference": "e55a6727052f91834a968937c93b6fb193be8fb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Atlantic18/DoctrineExtensions/zipball/e55a6727052f91834a968937c93b6fb193be8fb6", "reference": "e55a6727052f91834a968937c93b6fb193be8fb6", "shasum": ""}, "require": {"behat/transliterator": "~1.2", "doctrine/common": "~2.4", "php": ">=5.3.2"}, "conflict": {"doctrine/annotations": "<1.2", "doctrine/mongodb-odm": ">=2.0"}, "require-dev": {"doctrine/common": ">=2.5.0", "doctrine/mongodb-odm": ">=1.0.2 <2.0", "doctrine/orm": ">=2.5.0", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "symfony/yaml": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"doctrine/mongodb-odm": "to use the extensions with the MongoDB ODM", "doctrine/orm": "to use the extensions with the ORM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-4": {"Gedmo\\": "lib/G<PERSON>mo"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine2 behavioral extensions", "homepage": "http://gediminasm.org/", "keywords": ["Blameable", "behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree", "uploadable"], "time": "2020-05-10T22:20:03+00:00"}, {"name": "geoip2/geoip2", "version": "v2.10.0", "source": {"type": "git", "url": "https://github.com/maxmind/GeoIP2-php.git", "reference": "419557cd21d9fe039721a83490701a58c8ce784a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/GeoIP2-php/zipball/419557cd21d9fe039721a83490701a58c8ce784a", "reference": "419557cd21d9fe039721a83490701a58c8ce784a", "shasum": ""}, "require": {"ext-json": "*", "maxmind-db/reader": "~1.5", "maxmind/web-service-common": "~0.6", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "5.*", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"GeoIp2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind GeoIP2 PHP API", "homepage": "https://github.com/maxmind/GeoIP2-php", "keywords": ["IP", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2019-12-12T18:48:39+00:00"}, {"name": "google/recaptcha", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/google/recaptcha.git", "reference": "614f25a9038be4f3f2da7cbfd778dc5b357d2419"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/recaptcha/zipball/614f25a9038be4f3f2da7cbfd778dc5b357d2419", "reference": "614f25a9038be4f3f2da7cbfd778dc5b357d2419", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.2.20|^2.15", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^4.8.36|^5.7.27|^6.59|^7.5.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"ReCaptcha\\": "src/ReCaptcha"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Client library for reCAPTCHA, a free service that protects websites from spam and abuse.", "homepage": "https://www.google.com/recaptcha/", "keywords": ["Abuse", "<PERSON><PERSON>a", "recaptcha", "spam"], "time": "2020-03-31T17:50:54+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.4", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a4a1b6930528a8f7ee03518e6442ec7a44155d9d", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-05-25T19:35:05+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "hwi/oauth-bundle", "version": "0.6.3", "source": {"type": "git", "url": "https://github.com/hwi/HWIOAuthBundle.git", "reference": "0963709b04d8ac0d6d6c0c78787f6f59bd0d9a01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hwi/HWIOAuthBundle/zipball/0963709b04d8ac0d6d6c0c78787f6f59bd0d9a01", "reference": "0963709b04d8ac0d6d6c0c78787f6f59bd0d9a01", "shasum": ""}, "require": {"php": "^5.6|^7.0", "php-http/client-common": "^1.3", "php-http/client-implementation": "^1.0", "php-http/discovery": "^1.0", "php-http/httplug": "^1.0", "php-http/message-factory": "^1.0", "psr/http-message": "^1.0", "symfony/form": "^2.8|^3.0|^4.0", "symfony/framework-bundle": "^2.8|^3.0|^4.0", "symfony/options-resolver": "^2.8|^3.0|^4.0", "symfony/security-bundle": "^2.8|^3.0|^4.0", "symfony/templating": "^2.8|^3.0|^4.0", "symfony/yaml": "^2.8|^3.0|^4.0"}, "conflict": {"twig/twig": "<1.12"}, "require-dev": {"doctrine/orm": "^2.3", "friendsofphp/php-cs-fixer": "^2.0", "friendsofsymfony/user-bundle": "^1.3|^2.0", "php-http/guzzle6-adapter": "^1.1", "php-http/httplug-bundle": "^1.7", "phpunit/phpunit": "^5.7", "symfony/phpunit-bridge": "^2.8|^3.0|^4.0", "symfony/property-access": "^2.8|^3.0|^4.0", "symfony/stopwatch": "^2.8|^3.0|^4.0", "symfony/twig-bundle": "^2.8|^3.0|^4.0", "symfony/validator": "^2.8|^3.0|^4.0"}, "suggest": {"doctrine/doctrine-bundle": "to use Doctrine user provider", "friendsofsymfony/user-bundle": "to connect FOSUB with this bundle", "php-http/httplug-bundle": "to provide required HTTP client with ease.", "symfony/property-access": "to use FOSUB integration with this bundle", "symfony/twig-bundle": "to use the Twig hwi_oauth_* functions"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "0.6-dev"}}, "autoload": {"psr-4": {"HWI\\Bundle\\OAuthBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/hwi/HWIOAuthBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Support for authenticating users using both OAuth1.0a and OAuth2 in Symfony2.", "homepage": "http://github.com/hwi/HWIOAuthBundle", "keywords": ["37signals", "Authentication", "<PERSON><PERSON>", "EVE Online", "amazon", "asana", "auth0", "azure", "bitbucket", "bitly", "box", "bufferapp", "clever", "dailymotion", "deviantart", "discogs", "disqus", "dropbox", "eventbrite", "facebook", "firewall", "fiware", "flickr", "foursquare", "github", "gitlab", "google", "hubic", "instagram", "jawbone", "jira", "linkedin", "mail.ru", "o<PERSON>h", "oauth1", "oauth2", "odnoklassniki", "paypal", "qq", "reddit", "runkeeper", "salesforce", "security", "sensio connect", "sina weibo", "slack", "sound cloud", "spotify", "stack exchange", "stereomood", "strava", "toshl", "trakt", "trello", "twitch", "twitter", "vkontakte", "windows live", "wordpress", "wunderlist", "xing", "yahoo", "yandex", "youtube"], "time": "2018-07-31T10:19:28+00:00"}, {"name": "imagine/imagine", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/php-imagine/Imagine.git", "reference": "cb2361e5bb4410b681462d8e4f912bc5dabf84ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-imagine/Imagine/zipball/cb2361e5bb4410b681462d8e4f912bc5dabf84ab", "reference": "cb2361e5bb4410b681462d8e4f912bc5dabf84ab", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.2.*", "phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4"}, "suggest": {"ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-4": {"Imagine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "time": "2019-12-04T09:55:33+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "time": "2014-01-12T16:20:24+00:00"}, {"name": "jms/metadata", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "e5854ab1aa643623dc64adde718a8eec32b957a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/e5854ab1aa643623dc64adde718a8eec32b957a8", "reference": "e5854ab1aa643623dc64adde718a8eec32b957a8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"doctrine/cache": "~1.0", "symfony/cache": "~3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-0": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "time": "2018-10-26T12:40:10+00:00"}, {"name": "jms/parser-lib", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/parser-lib.git", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/parser-lib/zipball/c509473bc1b4866415627af0e1c6cc8ac97fa51d", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d", "shasum": ""}, "require": {"phpoption/phpoption": ">=0.9,<2.0-dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"JMS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "description": "A library for easily creating recursive-descent parsers.", "time": "2012-11-18T18:08:43+00:00"}, {"name": "jms/serializer", "version": "1.14.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "ba908d278fff27ec01fb4349f372634ffcd697c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/ba908d278fff27ec01fb4349f372634ffcd697c0", "reference": "ba908d278fff27ec01fb4349f372634ffcd697c0", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/instantiator": "^1.0.3", "jms/metadata": "^1.3", "jms/parser-lib": "1.*", "php": "^5.5|^7.0", "phpcollection/phpcollection": "~0.1", "phpoption/phpoption": "^1.1"}, "conflict": {"twig/twig": "<1.12"}, "require-dev": {"doctrine/orm": "~2.1", "doctrine/phpcr-odm": "^1.3|^2.0", "ext-pdo_sqlite": "*", "jackalope/jackalope-doctrine-dbal": "^1.1.5", "phpunit/phpunit": "^4.8|^5.0", "propel/propel1": "~1.7", "psr/container": "^1.0", "symfony/dependency-injection": "^2.7|^3.3|^4.0", "symfony/expression-language": "^2.6|^3.0", "symfony/filesystem": "^2.1", "symfony/form": "~2.1|^3.0", "symfony/translation": "^2.1|^3.0", "symfony/validator": "^2.2|^3.0", "symfony/yaml": "^2.1|^3.0", "twig/twig": "~1.12|~2.0"}, "suggest": {"doctrine/cache": "Required if you like to use cache functionality.", "doctrine/collections": "Required if you like to use doctrine collection types as ArrayCollection.", "symfony/yaml": "Required if you'd like to serialize data to YAML format."}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.14-dev"}}, "autoload": {"psr-0": {"JMS\\Serializer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Library for (de-)serializing data of any complexity; supports XML, JSON, and YAML.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2020-02-22T20:59:37+00:00"}, {"name": "jms/serializer-bundle", "version": "2.4.4", "source": {"type": "git", "url": "https://github.com/schmittjoh/JMSSerializerBundle.git", "reference": "92ee808c64c1c180775a0e57d00e3be0674668fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/JMSSerializerBundle/zipball/92ee808c64c1c180775a0e57d00e3be0674668fb", "reference": "92ee808c64c1c180775a0e57d00e3be0674668fb", "shasum": ""}, "require": {"jms/serializer": "^1.10", "php": "^5.4|^7.0", "phpoption/phpoption": "^1.1.0", "symfony/framework-bundle": "~2.3|~3.0|~4.0"}, "require-dev": {"doctrine/orm": "*", "phpunit/phpunit": "^4.8.35|^5.4.3|^6.0", "symfony/expression-language": "~2.6|~3.0|~4.0", "symfony/finder": "^2.3|^3.0|^4.0", "symfony/form": "*", "symfony/stopwatch": "*", "symfony/twig-bundle": "*", "symfony/validator": "*", "symfony/yaml": "*"}, "suggest": {"jms/di-extra-bundle": "Required to get lazy loading (de)serialization visitors, ~1.3", "symfony/finder": "Required for cache warmup, supported versions ^2.3|^3.0|^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"JMS\\SerializerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Allows you to easily serialize, and deserialize data of any complexity", "homepage": "http://jmsyst.com/bundles/JMSSerializerBundle", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2019-03-30T10:26:09+00:00"}, {"name": "jms/translation-bundle", "version": "1.4.4", "target-dir": "JMS/TranslationBundle", "source": {"type": "git", "url": "https://github.com/schmittjoh/JMSTranslationBundle.git", "reference": "7bdd5e2feae5c794af1306a408671a998bb84ca5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/JMSTranslationBundle/zipball/7bdd5e2feae5c794af1306a408671a998bb84ca5", "reference": "7bdd5e2feae5c794af1306a408671a998bb84ca5", "shasum": ""}, "require": {"nikic/php-parser": "^1.4 || ^2.0 || ^3.0 || ^4.0", "php": "^5.3.3 || ^7.0", "symfony/console": "^2.3 || ^3.0 || ^4.0", "symfony/framework-bundle": "^2.3 || ^3.0 || ^4.0", "symfony/validator": "^2.3 || ^3.0 || ^4.0", "twig/twig": "^1.27 || ^2.0"}, "require-dev": {"matthiasnoback/symfony-dependency-injection-test": "^1.2", "nyholm/nsa": "^1.0.1", "phpunit/phpunit": "^4.8 || ^5.0", "psr/log": "^1.0", "sensio/framework-extra-bundle": "^2.3 || ^3.0 || ^4.0", "symfony/expression-language": "^2.6 || ^3.0 || ^4.0", "symfony/symfony": "^2.3 || ^3.0 || ^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-0": {"JMS\\TranslationBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Puts the Symfony Translation Component on steroids", "homepage": "http://jmsyst.com/bundles/JMSTranslationBundle", "keywords": ["extract", "extraction", "i18n", "interface", "multilanguage", "translation", "ui", "webinterface"], "time": "2019-05-14T15:55:48+00:00"}, {"name": "knplabs/doctrine-behaviors", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/DoctrineBehaviors.git", "reference": "072078422bcada395906ec96b192c6b30a93bebb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/DoctrineBehaviors/zipball/072078422bcada395906ec96b192c6b30a93bebb", "reference": "072078422bcada395906ec96b192c6b30a93bebb", "shasum": ""}, "require": {"behat/transliterator": "~1.0", "doctrine/common": ">=2.2", "php": "~7.0"}, "require-dev": {"doctrine/orm": ">=2.4.5", "ext-pdo_mysql": "*", "ext-pdo_pgsql": "*", "ext-pdo_sqlite": "*", "hexmedia/yaml-linter": "~0.1", "jakub-onderka/php-parallel-lint": "~0.8", "phpunit/phpunit": "~4.8"}, "suggest": {"symfony/framework-bundle": "To be able to use it as a bundle"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Knp\\DoctrineBehaviors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Knplabs", "homepage": "http://knplabs.com"}], "description": "Doctrine2 behavior traits", "homepage": "http://knplabs.com", "keywords": ["Blameable", "behaviors", "doctrine2", "filterable", "softdeletable", "timestampable", "translatable", "tree"], "time": "2019-12-09T14:23:03+00:00"}, {"name": "knplabs/knp-components", "version": "v1.3.10", "source": {"type": "git", "url": "https://github.com/KnpLabs/knp-components.git", "reference": "fc1755ba2b77f83a3d3c99e21f3026ba2a1429be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/knp-components/zipball/fc1755ba2b77f83a3d3c99e21f3026ba2a1429be", "reference": "fc1755ba2b77f83a3d3c99e21f3026ba2a1429be", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"doctrine/mongodb-odm": "~1.0@beta", "doctrine/orm": "~2.4", "doctrine/phpcr-odm": "~1.2", "jackalope/jackalope-doctrine-dbal": "~1.2", "phpunit/phpunit": "~4.2", "ruflin/elastica": "~1.0", "symfony/event-dispatcher": "~2.5", "symfony/property-access": ">=2.3"}, "suggest": {"symfony/property-access": "To allow sorting arrays"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Knp\\Component": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/knp-components/contributors"}], "description": "Knplabs component library", "homepage": "http://github.com/KnpLabs/knp-components", "keywords": ["components", "knp", "knplabs", "pager", "paginator"], "time": "2018-09-11T07:54:48+00:00"}, {"name": "knplabs/knp-paginator-bundle", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpPaginatorBundle.git", "reference": "06100a8f3a1e878d3d2d5d06a61c4e648f94aed7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpPaginatorBundle/zipball/06100a8f3a1e878d3d2d5d06a61c4e648f94aed7", "reference": "06100a8f3a1e878d3d2d5d06a61c4e648f94aed7", "shasum": ""}, "require": {"knplabs/knp-components": "~1.2", "php": ">=5.6.0", "symfony/framework-bundle": "~2.7|~3.0|~4.0", "symfony/translation": "~2.7|~3.0|~4.0", "twig/twig": "~1.12|~2"}, "require-dev": {"phpunit/phpunit": "~4.8.35|~5.4.3|~6.4", "symfony/expression-language": "~2.7|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\PaginatorBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle/contributors"}], "description": "Paginator bundle for Symfony to automate pagination and simplify sorting and other features", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle", "keywords": ["bundle", "knp", "knplabs", "pager", "pagination", "paginator", "symfony"], "time": "2019-02-28T13:51:20+00:00"}, {"name": "knplabs/knp-time-bundle", "version": "v1.11.0", "target-dir": "Knp/Bundle/TimeBundle", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpTimeBundle.git", "reference": "8f3983f8b53e9c350f7dd476461bd7f5b32dfc89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpTimeBundle/zipball/8f3983f8b53e9c350f7dd476461bd7f5b32dfc89", "reference": "8f3983f8b53e9c350f7dd476461bd7f5b32dfc89", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "~3.4|^4.3|^5.0", "symfony/dependency-injection": "~3.4|^4.3|^5.0", "symfony/templating": "~3.4|^4.3|^5.0", "symfony/translation": "^4.3|^5.0"}, "require-dev": {"symfony/framework-bundle": "^4.3|^5.0", "symfony/phpunit-bridge": "^4.3|^5.0", "symfony/twig-bundle": "^4.3|^5.0"}, "suggest": {"symfony/twig-bundle": "to use the Twig time_diff function"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Knp\\Bundle\\TimeBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpTimeBundle/contributors"}], "description": "Knplabs time bundle makes your dates look sensible and descriptive", "homepage": "http://github.com/KnpLabs/KnpTimeBundle", "keywords": ["bundle", "date", "descriptive time", "knp", "knplabs", "time"], "time": "2019-12-30T16:00:41+00:00"}, {"name": "league/uri", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "f2bceb755f1108758cf4cf925e4cd7699ce686aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/f2bceb755f1108758cf4cf925e4cd7699ce686aa", "reference": "f2bceb755f1108758cf4cf925e4cd7699ce686aa", "shasum": ""}, "require": {"ext-fileinfo": "*", "ext-intl": "*", "ext-mbstring": "*", "league/uri-components": "^1.8", "league/uri-hostname-parser": "^1.1", "league/uri-interfaces": "^1.0", "league/uri-manipulations": "^1.5", "league/uri-parser": "^1.4", "league/uri-schemes": "^1.2", "php": ">=7.0.13", "psr/http-message": "^1.0"}, "type": "metapackage", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "uri", "url", "ws"], "time": "2018-03-14T17:19:39+00:00"}, {"name": "league/uri-components", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-components.git", "reference": "d0412fd730a54a8284009664188cf239070eae64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-components/zipball/d0412fd730a54a8284009664188cf239070eae64", "reference": "d0412fd730a54a8284009664188cf239070eae64", "shasum": ""}, "require": {"ext-curl": "*", "ext-fileinfo": "*", "ext-intl": "*", "league/uri-hostname-parser": "^1.1.0", "php": ">=7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.3", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI components manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["authority", "components", "fragment", "host", "path", "port", "query", "rfc3986", "scheme", "uri", "url", "userinfo"], "time": "2018-10-24T11:31:02+00:00"}, {"name": "league/uri-hostname-parser", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-hostname-parser.git", "reference": "7a6be3d06d0ed08dcb51f666aa60f3b66cd51325"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-hostname-parser/zipball/7a6be3d06d0ed08dcb51f666aa60f3b66cd51325", "reference": "7a6be3d06d0ed08dcb51f666aa60f3b66cd51325", "shasum": ""}, "require": {"ext-intl": "*", "php": ">=7.0", "psr/simple-cache": "^1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.7", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^6.3"}, "suggest": {"ext-curl": "To use the bundle cURL HTTP client", "psr/simple-cache-implementation": "To enable using other cache providers"}, "bin": ["bin/update-psl-icann-section"], "type": "library", "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://about.me/jere<PERSON><PERSON><PERSON>l", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://nyamsprod.com", "role": "Developer"}, {"name": "Contributors", "homepage": "https://github.com/phpleague/uri-hostname-parser/graphs/contributors"}], "description": "ICANN base hostname parsing implemented in PHP.", "homepage": "https://github.com/thephphleague/uri-hostname-parser", "keywords": ["Public Suffix List", "domain parsing", "icann"], "abandoned": true, "time": "2018-02-16T07:29:26+00:00"}, {"name": "league/uri-interfaces", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "081760c53a4ce76c9935a755a21353610f5495f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/081760c53a4ce76c9935a755a21353610f5495f6", "reference": "081760c53a4ce76c9935a755a21353610f5495f6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interface for URI representation", "homepage": "http://github.com/thephpleague/uri-interfaces", "keywords": ["rfc3986", "rfc3987", "uri", "url"], "time": "2018-11-05T14:00:06+00:00"}, {"name": "league/uri-manipulations", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-manipulations.git", "reference": "ae8d49a3203ccf7a1e39aaf7fae9f08bfbc454a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-manipulations/zipball/ae8d49a3203ccf7a1e39aaf7fae9f08bfbc454a2", "reference": "ae8d49a3203ccf7a1e39aaf7fae9f08bfbc454a2", "shasum": ""}, "require": {"ext-intl": "*", "league/uri-components": "^1.8.0", "league/uri-interfaces": "^1.0", "php": ">=7.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "guzzlehttp/psr7": "^1.2", "league/uri-schemes": "^1.2", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0", "zendframework/zend-diactoros": "1.4.0"}, "suggest": {"league/uri-schemes": "Allow manipulating URI objects"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://url.thephpleague.com", "keywords": ["formatter", "manipulation", "manipulations", "middlewares", "modifiers", "psr-7", "references", "rfc3986", "rfc3987", "uri", "url"], "abandoned": true, "time": "2018-03-14T16:44:57+00:00"}, {"name": "league/uri-parser", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-parser.git", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/671548427e4c932352d9b9279fdfa345bf63fa00", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-schemes": "Allow validating and normalizing URI parsing results"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "userland URI parser RFC 3986 compliant", "homepage": "https://github.com/thephpleague/uri-parser", "keywords": ["parse_url", "parser", "rfc3986", "rfc3987", "uri", "url"], "abandoned": true, "time": "2018-11-22T07:55:51+00:00"}, {"name": "league/uri-schemes", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-schemes.git", "reference": "f821a444785724bcc9bc244b1173b9d6ca4d71e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-schemes/zipball/f821a444785724bcc9bc244b1173b9d6ca4d71e6", "reference": "f821a444785724bcc9bc244b1173b9d6ca4d71e6", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/uri-interfaces": "^1.1", "league/uri-parser": "^1.4.0", "php": ">=7.0.13", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-manipulations": "Needed to easily manipulate URI objects"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["data-uri", "file", "ftp", "http", "https", "parse_url", "psr-7", "rfc3986", "uri", "url", "ws", "wss"], "abandoned": true, "time": "2018-11-26T08:09:30+00:00"}, {"name": "liip/imagine-bundle", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/liip/LiipImagineBundle.git", "reference": "06740a0a0e9c0054d3e0589472fd19b975784c52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/liip/LiipImagineBundle/zipball/06740a0a0e9c0054d3e0589472fd19b975784c52", "reference": "06740a0a0e9c0054d3e0589472fd19b975784c52", "shasum": ""}, "require": {"imagine/imagine": "^0.7.1|^1.1", "php": "^7.1", "symfony/asset": "^3.4|^4.3|^5.0", "symfony/filesystem": "^3.4|^4.3|^5.0", "symfony/finder": "^3.4|^4.3|^5.0", "symfony/framework-bundle": "^3.4|^4.3|^5.0", "symfony/options-resolver": "^3.4|^4.3|^5.0", "symfony/process": "^3.4|^4.3|^5.0", "symfony/templating": "^3.4|^4.3|^5.0", "symfony/translation": "^3.4|^4.3|^5.0"}, "require-dev": {"amazonwebservices/aws-sdk-for-php": "^1.0", "aws/aws-sdk-php": "^2.4", "doctrine/cache": "^1.1", "doctrine/orm": "^2.3", "enqueue/enqueue-bundle": "^0.9|^0.10", "ext-gd": "*", "friendsofphp/php-cs-fixer": "^2.10", "league/flysystem": "^1.0", "psr/log": "^1.0", "symfony/browser-kit": "^3.4|^4.3|^5.0", "symfony/console": "^3.4|^4.3|^5.0", "symfony/dependency-injection": "^3.4|^4.3|^5.0", "symfony/form": "^3.4|^4.3|^5.0", "symfony/phpunit-bridge": "^4.3|^5.0", "symfony/validator": "^3.4|^4.3|^5.0", "symfony/yaml": "^3.4|^4.3|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"alcaeus/mongo-php-adapter": "required on PHP >= 7.0 to use mongo components with mongodb extension", "amazonwebservices/aws-sdk-for-php": "required to use AWS version 1 cache resolver", "aws/aws-sdk-php": "required to use AWS version 2/3 cache resolver", "doctrine/mongodb-odm": "required to use mongodb-backed doctrine components", "enqueue/enqueue-bundle": "^0.9 add if you like to process images in background", "ext-exif": "required to read EXIF metadata from images", "ext-gd": "required to use gd driver", "ext-gmagick": "required to use gmagick driver", "ext-imagick": "required to use imagick driver", "ext-mongo": "required for mongodb components on PHP <7.0", "ext-mongodb": "required for mongodb components on PHP >=7.0", "league/flysystem": "required to use FlySystem data loader or cache resolver", "monolog/monolog": "A psr/log compatible logger is required to enable logging", "twig/twig": "required to use the provided Twig extension. Version 1.12 or greater needed"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-1.0": "1.7-dev", "dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Liip\\ImagineBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON> and other contributors", "homepage": "https://github.com/liip/LiipImagineBundle/contributors"}], "description": "This bundle provides an image manipulation abstraction toolkit for Symfony-based projects.", "homepage": "http://liip.ch", "keywords": ["bundle", "image", "imagine", "liip", "manipulation", "photos", "pictures", "symfony", "transformation"], "time": "2020-01-07T13:29:56+00:00"}, {"name": "markbaker/complex", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/8eaa40cceec7bf0518187530b2e63871be661b72", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2020-03-11T20:15:49+00:00"}, {"name": "markbaker/matrix", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/5348c5a67e3b75cd209d70103f916a93b1f1ed21", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2019-10-06T11:29:25+00:00"}, {"name": "maxmind-db/reader", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "febd4920bf17c1da84cef58e56a8227dfb37fbe4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/febd4920bf17c1da84cef58e56a8227dfb37fbe4", "reference": "febd4920bf17c1da84cef58e56a8227dfb37fbe4", "shasum": ""}, "require": {"php": ">=5.6"}, "conflict": {"ext-maxminddb": "<1.6.0,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpcov": "^3.0", "phpunit/phpunit": "5.*", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2019-12-19T22:59:03+00:00"}, {"name": "maxmind/web-service-common", "version": "v0.7.0", "source": {"type": "git", "url": "https://github.com/maxmind/web-service-common-php.git", "reference": "74c996c218ada5c639c8c2f076756e059f5552fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/web-service-common-php/zipball/74c996c218ada5c639c8c2f076756e059f5552fc", "reference": "74c996c218ada5c639c8c2f076756e059f5552fc", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0.3", "ext-curl": "*", "ext-json": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Exception\\": "src/Exception", "MaxMind\\WebService\\": "src/WebService"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Internal MaxMind Web Service API", "homepage": "https://github.com/maxmind/web-service-common-php", "time": "2020-05-06T14:07:26+00:00"}, {"name": "mercadopago/dx-php", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/mercadopago/sdk-php.git", "reference": "cfaf5893def0bcd95ecb863f1822bc225c398a04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mercadopago/sdk-php/zipball/cfaf5893def0bcd95ecb863f1822bc225c398a04", "reference": "cfaf5893def0bcd95ecb863f1822bc225c398a04", "shasum": ""}, "require": {"doctrine/annotations": "^1.8", "doctrine/common": "^2.6 || ^3.0", "php": ">=7.1.0"}, "require-dev": {"doctrine/orm": "^2.3", "phpmd/phpmd": "@stable", "phpunit/phpunit": "^7", "sebastian/phpcpd": "*", "squizlabs/php_codesniffer": "2.8.1", "symfony/yaml": "~2.5", "vlucas/phpdotenv": "^2.5"}, "type": "library", "autoload": {"psr-4": {"MercadoPago\\": ["src/MercadoPago/", "tests/", "src/MercadoPago/Generic/", "src/MercadoPago/Entities/", "src/MercadoPago/Entities/Shared/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Mercado Pago PHP SDK", "homepage": "https://github.com/mercadopago/sdk-php", "time": "2023-09-22T13:06:46+00:00"}, {"name": "monolog/monolog", "version": "1.25.4", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "3022efff205e2448b560c833c6fbbf91c3139168"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/3022efff205e2448b560c833c6fbbf91c3139168", "reference": "3022efff205e2448b560c833c6fbbf91c3139168", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-05-22T07:31:27+00:00"}, {"name": "nelmio/cors-bundle", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioCorsBundle.git", "reference": "9683e6d30d000ef998919261329d825de7c53499"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioCorsBundle/zipball/9683e6d30d000ef998919261329d825de7c53499", "reference": "9683e6d30d000ef998919261329d825de7c53499", "shasum": ""}, "require": {"symfony/framework-bundle": "^4.3 || ^5.0"}, "require-dev": {"mockery/mockery": "^1.2", "symfony/phpunit-bridge": "^4.3 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\CorsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nelmio", "homepage": "http://nelm.io"}, {"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioCorsBundle/contributors"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Symfony application", "keywords": ["api", "cors", "crossdomain"], "time": "2019-11-15T08:54:08+00:00"}, {"name": "nikic/php-parser", "version": "v4.4.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "bd43ec7152eaaab3bd8c6d0aa95ceeb1df8ee120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/bd43ec7152eaaab3bd8c6d0aa95ceeb1df8ee120", "reference": "bd43ec7152eaaab3bd8c6d0aa95ceeb1df8ee120", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "0.0.5", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2020-04-10T16:34:50+00:00"}, {"name": "ninsuo/symfony-collection", "version": "2.1.33", "source": {"type": "git", "url": "https://github.com/ninsuo/symfony-collection.git", "reference": "231f790eee9ea2c3301ac45f5fe4fbb09ec4289c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ninsuo/symfony-collection/zipball/231f790eee9ea2c3301ac45f5fe4fbb09ec4289c", "reference": "231f790eee9ea2c3301ac45f5fe4fbb09ec4289c", "shasum": ""}, "type": "library", "autoload": {"classmap": ["ScriptHandler.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A jQuery plugin that manages adding, deleting and moving elements from a Symfony collection", "time": "2020-01-13T12:54:48+00:00"}, {"name": "ocramius/package-versions", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/Ocramius/PackageVersions.git", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0.0", "php": "^7.1.0"}, "require-dev": {"composer/composer": "^1.6.3", "doctrine/coding-standard": "^5.0.1", "ext-zip": "*", "infection/infection": "^0.7.1", "phpunit/phpunit": "^7.5.17"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "time": "2019-11-15T16:17:10+00:00"}, {"name": "ocramius/proxy-manager", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/Ocramius/ProxyManager.git", "reference": "e18ac876b2e4819c76349de8f78ccc8ef1554cd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/e18ac876b2e4819c76349de8f78ccc8ef1554cd7", "reference": "e18ac876b2e4819c76349de8f78ccc8ef1554cd7", "shasum": ""}, "require": {"ocramius/package-versions": "^1.1.1", "php": "^7.1.0", "zendframework/zend-code": "^3.1.0"}, "require-dev": {"couscous/couscous": "^1.5.2", "ext-phar": "*", "humbug/humbug": "dev-master@DEV", "nikic/php-parser": "^3.0.4", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "^0.6.4", "phpunit/phpunit": "^5.6.4", "phpunit/phpunit-mock-objects": "^3.4.1", "squizlabs/php_codesniffer": "^2.7.0"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-0": {"ProxyManager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "description": "A library providing utilities to generate, instantiate and generally operate with Object Proxies", "homepage": "https://github.com/Ocramius/ProxyManager", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "time": "2017-05-04T11:12:50+00:00"}, {"name": "paypal/rest-api-sdk-php", "version": "1.14.0", "source": {"type": "git", "url": "https://github.com/paypal/PayPal-PHP-SDK.git", "reference": "72e2f2466975bf128a31e02b15110180f059fc04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paypal/PayPal-PHP-SDK/zipball/72e2f2466975bf128a31e02b15110180f059fc04", "reference": "72e2f2466975bf128a31e02b15110180f059fc04", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.3.0", "psr/log": "^1.0.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"PayPal": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "PayPal", "homepage": "https://github.com/paypal/rest-api-sdk-php/contributors"}], "description": "PayPal's PHP SDK for REST APIs", "homepage": "http://paypal.github.io/PayPal-PHP-SDK/", "keywords": ["payments", "paypal", "rest", "sdk"], "abandoned": true, "time": "2019-01-04T20:04:25+00:00"}, {"name": "payum/core", "version": "1.6.0", "target-dir": "Payum/Core", "source": {"type": "git", "url": "https://github.com/Payum/Core.git", "reference": "43954de6415e1a29fa09d7ca7949b071741afbc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Payum/Core/zipball/43954de6415e1a29fa09d7ca7949b071741afbc6", "reference": "43954de6415e1a29fa09d7ca7949b071741afbc6", "shasum": ""}, "require": {"league/uri": "^5.0", "payum/iso4217": "^1.0", "php": "^7.0", "php-http/client-implementation": "^1.0", "php-http/message": "^1.0", "twig/twig": "^1.0|^2.0"}, "require-dev": {"defuse/php-encryption": "^2", "doctrine/orm": "2.*", "ext-curl": "*", "ext-pdo_sqlite": "*", "omnipay/common": "^3@dev", "omnipay/dummy": "^3@alpha", "payum/omnipay-v3-bridge": "^1@alpha", "php-http/guzzle6-adapter": "^1.0", "phpunit/phpunit": "^5.7", "propel/propel1": "~1.7", "psr/log": "~1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/form": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/phpunit-bridge": "^4.4|^5.0", "symfony/routing": "^4.4|^5.0", "symfony/validator": "^4.4|^5.0", "zendframework/zend-db": "~2"}, "suggest": {"defuse/php-encryption": "^2 If you want to encrypt gateways credentials in database", "doctrine/mongodb-odm": "~1.1 If you want to store models to mongo doctrin2 ODM", "doctrine/orm": "~2.3 If you want to store models to database using doctrin2 ORM", "monolog/monolog": "~1.0 If you want to use PSR-3 logger", "payum/authorize-net-aim": "self.version If you want to use Authorize.Net AIM payment gateway", "payum/be2bill": "self.version If you want to use be2bill payment gateway", "payum/omnipay-v3-bridge": "^1 If you want to use omnipay's gateways", "payum/payex": "self.version If you want to use payex payment gateway", "payum/paypal-express-checkout-nvp": "self.version If you want to use paypal express checkout, digital goods or recurring payments", "payum/paypal-ipn": "self.version If you want to use paypal instant payment notifications(Paypal IPN)", "payum/paypal-pro-checkout-nvp": "self.version If you want to use paypal pro checkout", "payum/paypal-rest": "self.version If you want to use paypal rest gateway", "propel/propel": "If you want to store models to Propel2 ORM", "propel/propel1": "~1.7 If you want to store models to Propel1 ORM", "symfony/dependency-injection": "~2.8|~3.0 If you want to use container aware stuff", "symfony/form": "~2.8|~3.0 If you want to use forms", "symfony/http-foundation": "~2.8|~3.0 If you want to use HttpRequestVerifier or HttpResponse reply from symfony's bridge", "symfony/http-kernel": "~2.8|~3.0 If you want to use HttpRequestVerifier from symfony's bridge", "symfony/routing": "~2.8|~3.0 If you want to use TokenFactory from symfony's bridge", "zendframework/zend-db": "~2.0 If you want to store models to Zend Db ORM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Payum\\Core\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Payum project", "homepage": "https://payum.forma-pro.com/"}, {"name": "Community contributions", "homepage": "https://github.com/Payum/Payum/contributors"}], "description": "One million downloads of Payum already! Payum offers everything you need to work with payments. Friendly for all PHP frameworks (Symfony, Laravel, Zend, Yii, Silex). Check more visiting site.", "homepage": "https://payum.forma-pro.com/", "keywords": ["authorize", "capture", "notify", "payment", "payout", "recurring payment", "refund", "subscription", "withdrawal"], "time": "2020-02-11T22:12:10+00:00"}, {"name": "payum/iso4217", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/Payum/iso4217.git", "reference": "6a45480e2818350dea58b7a076d0115aa7ff5789"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Payum/iso4217/zipball/6a45480e2818350dea58b7a076d0115aa7ff5789", "reference": "6a45480e2818350dea58b7a076d0115aa7ff5789", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Payum\\ISO4217\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Community contributions", "homepage": "https://github.com/Payum/Payum/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Payum project", "homepage": "http://payum.org/"}], "description": "ISO 4217 PHP Library", "homepage": "http://payum.org", "keywords": ["4217", "ISO 4217", "currencies", "iso", "library"], "time": "2016-08-04T08:15:12+00:00"}, {"name": "payum/offline", "version": "1.6.0", "target-dir": "Payum/Offline", "source": {"type": "git", "url": "https://github.com/Payum/Offline.git", "reference": "b92fabfe2b1842f752250dacdb2e17217f2941c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Payum/Offline/zipball/b92fabfe2b1842f752250dacdb2e17217f2941c7", "reference": "b92fabfe2b1842f752250dacdb2e17217f2941c7", "shasum": ""}, "require": {"payum/core": "^1.5"}, "require-dev": {"payum/core": "^1.5", "phpunit/phpunit": "^5.7", "symfony/phpunit-bridge": "^3.1|^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Payum\\Offline": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Payum project", "homepage": "https://payum.forma-pro.com/"}, {"name": "Community contributions", "homepage": "https://github.com/Payum/Offline/contributors"}], "description": "The Payum extension. It provides Offline payment integration.", "homepage": "https://payum.forma-pro.com", "keywords": ["invoice", "offlile", "payment"], "time": "2019-11-23T14:41:43+00:00"}, {"name": "payum/paypal-express-checkout-nvp", "version": "1.6.0", "target-dir": "Payum/Paypal/ExpressCheckout/Nvp", "source": {"type": "git", "url": "https://github.com/Payum/PaypalExpressCheckoutNvp.git", "reference": "937023f6d49da62df700c3861e1ad07709590dbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Payum/PaypalExpressCheckoutNvp/zipball/937023f6d49da62df700c3861e1ad07709590dbb", "reference": "937023f6d49da62df700c3861e1ad07709590dbb", "shasum": ""}, "require": {"payum/core": "^1.5"}, "require-dev": {"payum/core": "^1.5", "php-http/guzzle6-adapter": "^1.0", "phpunit/phpunit": "^5.7", "symfony/phpunit-bridge": "^3.1|^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Payum\\Paypal\\ExpressCheckout\\Nvp": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Payum project", "homepage": "https://payum.forma-pro.com/"}, {"name": "Community contributions", "homepage": "https://github.com/Payum/PaypalExpressCheckoutNvp/contributors"}], "description": "The Payum extension. It provides Paypal ExpressCheckout payment integration.", "homepage": "https://payum.forma-pro.com/", "keywords": ["digital goods", "express checkout", "payment", "paypal", "recurring payment"], "time": "2019-11-23T14:41:43+00:00"}, {"name": "payum/payum-bundle", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/Payum/PayumBundle.git", "reference": "08416446c546d3aca218b91b237c2a25d147afbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Payum/PayumBundle/zipball/08416446c546d3aca218b91b237c2a25d147afbc", "reference": "08416446c546d3aca218b91b237c2a25d147afbc", "shasum": ""}, "require": {"payum/core": "^1.5", "php": "^7.1.3", "symfony/form": "^4.4|^5", "symfony/framework-bundle": "^4.4|^5", "symfony/security-csrf": "^4.4|^5", "symfony/validator": "^4.4|^5"}, "require-dev": {"defuse/php-encryption": "^2", "doctrine/orm": "~2.5", "ext-curl": "*", "ext-pdo_sqlite": "*", "ext-soap": "*", "fp/klarna-invoice": "0.1.*", "klarna/checkout": "~1|~2.0", "omnipay/common": "^3@dev", "omnipay/dummy": "^3@alpha", "omnipay/paypal": "^3@dev", "paypal/rest-api-sdk-php": "0.5.*", "payum/omnipay-v3-bridge": "^1@alpha", "payum/payum": "^1.5@dev", "php-http/guzzle6-adapter": "^1", "phpunit/phpunit": "^7.5", "stripe/stripe-php": "~1.0", "symfony/browser-kit": "^4.4|^5", "symfony/expression-language": "^4.4|^5", "symfony/phpunit-bridge": "^4.4|^5", "symfony/templating": "^4.4|^5", "symfony/twig-bundle": "^4.4|^5", "symfony/yaml": "^4.4|^5", "twig/twig": "^1.16|^2.0"}, "suggest": {"sonata-project/admin-bundle": "^3 If you want to configure payments in the backend."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Payum\\Bundle\\PayumBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Payum project", "homepage": "https://payum.forma-pro.com/"}, {"name": "Community contributions", "homepage": "https://github.com/Payum/PayumBundle/contributors"}], "description": "One million downloads of Payum already! Payum offers everything you need to work with payments. Check more visiting site.", "homepage": "https://payum.forma-pro.com/", "keywords": ["authorize.net", "be2bill", "instant notifications", "klarna", "offline", "omnipay", "payex", "payment", "paypal", "paypal express checkout", "paypal pro checkout", "recurring payment", "stripe", "stripe checkout", "stripe.js", "symfony"], "time": "2020-02-11T19:51:20+00:00"}, {"name": "payum/stripe", "version": "1.5.1", "target-dir": "Payum/Stripe", "source": {"type": "git", "url": "https://github.com/Payum/Stripe.git", "reference": "b0e324738ec85dba5901fde992fdc2f63e1de9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Payum/Stripe/zipball/b0e324738ec85dba5901fde992fdc2f63e1de9be", "reference": "b0e324738ec85dba5901fde992fdc2f63e1de9be", "shasum": ""}, "require": {"payum/core": "^1.5", "stripe/stripe-php": "^3|^4"}, "require-dev": {"payum/core": "^1.5", "phpunit/phpunit": "^5.7", "symfony/phpunit-bridge": "^3.1|^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Payum\\Stripe": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Community contributions", "homepage": "https://github.com/Payum/Stripe/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Payum project", "homepage": "https://payum.forma-pro.com/"}], "description": "The Payum extension. It provides Stripe payment integration.", "homepage": "https://payum.forma-pro.com", "keywords": ["checkout", "direct", "payment", "store card", "stripe", "stripe.js", "subscription"], "time": "2018-11-06T16:28:25+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.2", "source": {"type": "git", "url": "https://github.com/PhenX/php-font-lib.git", "reference": "ca6ad461f032145fff5971b5985e5af9e7fa88d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-font-lib/zipball/ca6ad461f032145fff5971b5985e5af9e7fa88d8", "reference": "ca6ad461f032145fff5971b5985e5af9e7fa88d8", "shasum": ""}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5 || ^6 || ^7"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "time": "2020-03-08T15:31:32+00:00"}, {"name": "phenx/php-svg-lib", "version": "v0.3.3", "source": {"type": "git", "url": "https://github.com/PhenX/php-svg-lib.git", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-svg-lib/zipball/5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "shasum": ""}, "require": {"sabberworm/php-css-parser": "^8.3"}, "require-dev": {"phpunit/phpunit": "^5.5|^6.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "time": "2019-09-11T20:02:13+00:00"}, {"name": "php-http/client-common", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "c0390ae3c8f2ae9d50901feef0127fb9e396f6b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/c0390ae3c8f2ae9d50901feef0127fb9e396f6b4", "reference": "c0390ae3c8f2ae9d50901feef0127fb9e396f6b4", "shasum": ""}, "require": {"php": "^5.4 || ^7.0", "php-http/httplug": "^1.1", "php-http/message": "^1.6", "php-http/message-factory": "^1.0", "symfony/options-resolver": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"guzzlehttp/psr7": "^1.4", "phpspec/phpspec": "^2.5 || ^3.4 || ^4.2"}, "suggest": {"php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "time": "2019-11-18T08:54:36+00:00"}, {"name": "php-http/discovery", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82dbef649ccffd8e4f22e1953c3a5265992b83c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82dbef649ccffd8e4f22e1953c3a5265992b83c0", "reference": "82dbef649ccffd8e4f22e1953c3a5265992b83c0", "shasum": ""}, "require": {"php": "^7.1"}, "conflict": {"nyholm/psr7": "<1.0"}, "require-dev": {"akeneo/phpspec-skip-example-extension": "^4.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1", "puli/composer-plugin": "1.0.0-beta10"}, "suggest": {"php-http/message": "Allow to use Guzzle, Diactoros or Slim Framework factories", "puli/composer-plugin": "Sets up Puli which is recommended for Discovery to work. Check http://docs.php-http.org/en/latest/discovery.html for more details."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds installed HTTPlug implementations and PSR-7 message factories", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr7"], "time": "2020-01-03T11:25:47+00:00"}, {"name": "php-http/guzzle6-adapter", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/php-http/guzzle6-adapter.git", "reference": "a56941f9dc6110409cfcddc91546ee97039277ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle6-adapter/zipball/a56941f9dc6110409cfcddc91546ee97039277ab", "reference": "a56941f9dc6110409cfcddc91546ee97039277ab", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0", "php": ">=5.5.0", "php-http/httplug": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "php-http/adapter-integration-tests": "^0.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle6\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 6 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "abandoned": "guzzlehttp/guzzle or php-http/guzzle7-adapter", "time": "2016-05-10T06:13:32+00:00"}, {"name": "php-http/httplug", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "shasum": ""}, "require": {"php": ">=5.4", "php-http/promise": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "time": "2016-08-31T08:30:17+00:00"}, {"name": "php-http/httplug-bundle", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/php-http/HttplugBundle.git", "reference": "5044b655fcd3a43243383cd692a6bb6cd18af24f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/HttplugBundle/zipball/5044b655fcd3a43243383cd692a6bb6cd18af24f", "reference": "5044b655fcd3a43243383cd692a6bb6cd18af24f", "shasum": ""}, "require": {"php": "^7.1", "php-http/client-common": "^1.9 || ^2.0", "php-http/client-implementation": "^1.0", "php-http/discovery": "^1.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/logger-plugin": "^1.1", "php-http/message": "^1.4", "php-http/message-factory": "^1.0.2", "php-http/stopwatch-plugin": "^1.2", "psr/http-message": "^1.0", "symfony/config": "^3.4.20 || ^4.2.1", "symfony/dependency-injection": "^3.4.20 || ^4.2.1", "symfony/event-dispatcher": "^3.4.20 || ^4.2.1", "symfony/http-kernel": "^3.4.20 || ^4.2.1", "symfony/options-resolver": "^3.4.20 || ^4.2.1"}, "conflict": {"php-http/curl-client": "<2.0", "php-http/guzzle6-adapter": "<1.1"}, "require-dev": {"guzzlehttp/psr7": "^1.0", "matthiasnoback/symfony-dependency-injection-test": "^3.0", "nyholm/nsa": "^1.1", "php-http/cache-plugin": "^1.6", "php-http/guzzle6-adapter": "^1.1.1 || ^2.0.1", "php-http/mock-client": "^1.2", "php-http/promise": "^1.0", "polishsymfonycommunity/symfony-mocker-container": "^1.0", "symfony/browser-kit": "^3.4.20 || ^4.2.1", "symfony/cache": "^3.4.20 || ^4.2.1", "symfony/dom-crawler": "^3.4.20 || ^4.2.1", "symfony/framework-bundle": "^3.4.0 || ^4.2", "symfony/http-foundation": "^3.4.20 || ^4.2.1", "symfony/phpunit-bridge": "^3.4 || ^4.2", "symfony/stopwatch": "^3.4.20 || ^4.2.1", "symfony/twig-bundle": "^3.4.20 || ^4.2.1", "symfony/web-profiler-bundle": "^3.4.20 || ^4.2.1", "twig/twig": "^1.36 || ^2.6"}, "suggest": {"php-http/cache-plugin": "To configure clients that cache responses", "php-http/mock-client": "Add this to your require-dev section to mock HTTP responses easily", "twig/twig": "Add this to your require-dev section when using the WebProfilerBundle"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\HttplugBundle\\": "src/"}, "exclude-from-classmap": ["/Tests/Resources/MyPsr18TestClient.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony integration for HTTPlug", "homepage": "http://httplug.io", "keywords": ["adapter", "bundle", "discovery", "factory", "http", "httplug", "message", "php-http"], "time": "2019-06-05T12:03:16+00:00"}, {"name": "php-http/logger-plugin", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/logger-plugin.git", "reference": "c1c6e90717ce350319b7b8bc489f1db35bb523fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/logger-plugin/zipball/c1c6e90717ce350319b7b8bc489f1db35bb523fd", "reference": "c1c6e90717ce350319b7b8bc489f1db35bb523fd", "shasum": ""}, "require": {"php": "^5.4 || ^7.0", "php-http/client-common": "^1.9 || ^2.0", "php-http/message": "^1.0", "psr/log": "^1.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Client\\Common\\Plugin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PSR-3 Logger plugin for HTTPlug", "homepage": "http://httplug.io", "keywords": ["http", "httplug", "logger", "plugin"], "time": "2019-01-30T11:48:21+00:00"}, {"name": "php-http/message", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "ce8f43ac1e294b54aabf5808515c3554a19c1e1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/ce8f43ac1e294b54aabf5808515c3554a19c1e1c", "reference": "ce8f43ac1e294b54aabf5808515c3554a19c1e1c", "shasum": ""}, "require": {"clue/stream-filter": "^1.4", "php": "^7.1", "php-http/message-factory": "^1.0.2", "psr/http-message": "^1.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"akeneo/phpspec-skip-example-extension": "^1.0", "coduo/phpspec-data-provider-extension": "^1.0", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0", "henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4", "slim/slim": "^3.0", "zendframework/zend-diactoros": "^1.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation", "zendframework/zend-diactoros": "Used with Diactoros Factories"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}, "files": ["src/filters.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "time": "2019-08-05T06:55:08+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "abandoned": "psr/http-factory", "time": "2015-12-19T14:08:53+00:00"}, {"name": "php-http/promise", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "dc494cdc9d7160b9a09bd5573272195242ce7980"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/dc494cdc9d7160b9a09bd5573272195242ce7980", "reference": "dc494cdc9d7160b9a09bd5573272195242ce7980", "shasum": ""}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "time": "2016-01-26T13:27:02+00:00"}, {"name": "php-http/stopwatch-plugin", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/php-http/stopwatch-plugin.git", "reference": "de6f39c96f57c60a43d535e27122de505e683f98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/stopwatch-plugin/zipball/de6f39c96f57c60a43d535e27122de505e683f98", "reference": "de6f39c96f57c60a43d535e27122de505e683f98", "shasum": ""}, "require": {"php": "^7.1", "php-http/client-common": "^1.9 || ^2.0", "symfony/stopwatch": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.0 || ^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Http\\Client\\Common\\Plugin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony Stopwatch plugin for HTTPlug", "homepage": "http://httplug.io", "keywords": ["http", "httplug", "plugin", "stopwatch"], "time": "2019-11-18T08:10:48+00:00"}, {"name": "phpcollection/phpcollection", "version": "0.5.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-collection.git", "reference": "f2bcff45c0da7c27991bbc1f90f47c4b7fb434a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-collection/zipball/f2bcff45c0da7c27991bbc1f90f47c4b7fb434a6", "reference": "f2bcff45c0da7c27991bbc1f90f47c4b7fb434a6", "shasum": ""}, "require": {"phpoption/phpoption": "1.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.4-dev"}}, "autoload": {"psr-0": {"PhpCollection": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "General-Purpose Collection Library for PHP", "keywords": ["collection", "list", "map", "sequence", "set"], "time": "2015-05-17T12:39:23+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/6568f4687e5b41b054365f9ae03fcb1ed5f2069b", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-04-27T09:25:28+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/2e32a6d48972b2c1976ed5d8967145b6cec4a4a9", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9", "shasum": ""}, "require": {"php": "^7.1", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "^7.1", "mockery/mockery": "~1", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2019-08-22T18:11:29+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2020-04-27T08:12:48+00:00"}, {"name": "phpoption/phpoption", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "4acfd6a4b33a509d8c88f50e5222f734b6aeebae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/4acfd6a4b33a509d8c88f50e5222f734b6aeebae", "reference": "4acfd6a4b33a509d8c88f50e5222f734b6aeebae", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.3", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2020-03-21T18:07:53+00:00"}, {"name": "presta/sitemap-bundle", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/prestaconcept/PrestaSitemapBundle.git", "reference": "cc8ed9378f7ac713fcd0d6e39836b584aa939614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/prestaconcept/PrestaSitemapBundle/zipball/cc8ed9378f7ac713fcd0d6e39836b584aa939614", "reference": "cc8ed9378f7ac713fcd0d6e39836b584aa939614", "shasum": ""}, "require": {"php": ">=7.1.0", "symfony/console": "^3.4|~4.0|~5.0", "symfony/framework-bundle": "^3.4|~4.0|~5.0"}, "require-dev": {"doctrine/annotations": "~1.0", "phpunit/phpunit": "7.*", "symfony/browser-kit": "^3.4|~4.0|~5.0", "symfony/form": "^3.4|~4.0|~5.0", "symfony/phpunit-bridge": "^3.4|~4.0|~5.0", "symfony/security-bundle": "^3.4|~4.0|~5.0", "symfony/translation": "^3.4|~4.0|~5.0", "symfony/validator": "^3.4|~4.0|~5.0", "symfony/yaml": "^3.4|~4.0|~5.0"}, "suggest": {"doctrine/doctrine-cache-bundle": "Allows to store sitemaps in cache"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev", "1.x": "1.x-dev"}}, "autoload": {"psr-4": {"Presta\\SitemapBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Prestaconcept", "homepage": "http://www.prestaconcept.net/"}], "description": "A Symfony bundle that provides tools to build your application sitemap.", "keywords": ["Sitemap", "bundle", "prestaconcept", "symfony", "xml"], "time": "2020-03-02T12:08:58+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "pugx/autocompleter-bundle", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/PUGX/PUGXAutoCompleterBundle.git", "reference": "55abcee0e3d59ef0a3f017ce21c55bf5a502be8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PUGX/PUGXAutoCompleterBundle/zipball/55abcee0e3d59ef0a3f017ce21c55bf5a502be8e", "reference": "55abcee0e3d59ef0a3f017ce21c55bf5a502be8e", "shasum": ""}, "require": {"doctrine/common": "^2.8", "php": "^7.1", "symfony/config": "^3.4 || ^4.3 || ^5.0", "symfony/dependency-injection": "^3.4 || ^4.3 || ^5.0", "symfony/event-dispatcher": "^3.4 || ^4.3 || ^5.0", "symfony/form": "^3.4 || ^4.3 || ^5.0", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0"}, "require-dev": {"lexik/form-filter-bundle": "^5.0", "phpunit/phpunit": "^7.5 || ^8.4"}, "suggest": {"lexik/form-filter-bundle": "To use autocomplete on a filter."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"PUGX\\AutocompleterBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Add an autocomplete type to forms", "keywords": ["field", "form", "symfony"], "time": "2019-11-30T12:26:33+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "sabberworm/php-css-parser", "version": "8.3.0", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "91bcc3e3fdb7386c9a2e0e0aa09ca75cc43f121f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/91bcc3e3fdb7386c9a2e0e0aa09ca75cc43f121f", "reference": "91bcc3e3fdb7386c9a2e0e0aa09ca75cc43f121f", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-0": {"Sabberworm\\CSS": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "http://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "time": "2019-02-22T07:42:52+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.5.5", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "c76bb1c5c67840ecb6d9be8e9d8d7036e375e317"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/c76bb1c5c67840ecb6d9be8e9d8d7036e375e317", "reference": "c76bb1c5c67840ecb6d9be8e9d8d7036e375e317", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": ">=7.1.3", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"doctrine/doctrine-cache-bundle": "<1.3.1"}, "require-dev": {"doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/monolog-bridge": "^4.0|^5.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^4.3.5|^5.0", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.5.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": "src/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "abandoned": "Symfony", "time": "2020-05-06T12:12:33+00:00"}, {"name": "stof/doctrine-extensions-bundle", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/stof/StofDoctrineExtensionsBundle.git", "reference": "4d340daa1a8304faa62260be2adb0180e2138af3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stof/StofDoctrineExtensionsBundle/zipball/4d340daa1a8304faa62260be2adb0180e2138af3", "reference": "4d340daa1a8304faa62260be2adb0180e2138af3", "shasum": ""}, "require": {"gedmo/doctrine-extensions": "^2.3.4", "php": "^7.1.3", "symfony/framework-bundle": "^4.3 || ^5.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.4", "symfony/security-bundle": "^4.3 || ^5.0"}, "suggest": {"doctrine/doctrine-bundle": "to use the ORM extensions", "doctrine/mongodb-odm-bundle": "to use the MongoDB ODM extensions", "symfony/mime": "To use the Mime component integration for Uploadable"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Stof\\DoctrineExtensionsBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Integration of the gedmo/doctrine-extensions with Symfony2", "homepage": "https://github.com/stof/StofDoctrineExtensionsBundle", "keywords": ["behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree"], "time": "2020-03-30T09:00:40+00:00"}, {"name": "stripe/stripe-php", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "834681b9cdfa68d88a27e36ec9e356b598a5f0e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/834681b9cdfa68d88a27e36ec9e356b598a5f0e6", "reference": "834681b9cdfa68d88a27e36ec9e356b598a5f0e6", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "~0.6.1", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "time": "2017-06-20T01:39:06+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.3", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "shasum": ""}, "require": {"egulias/email-validator": "~2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses", "true/punycode": "Needed to support internationalized email addresses, if ext-intl is not installed"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "abandoned": "symfony/mailer", "time": "2019-11-12T09:31:26+00:00"}, {"name": "symfony/asset", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "fc8eff5841b549fbd66f89e1fd7cfb6a823ee512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/fc8eff5841b549fbd66f89e1fd7cfb6a823ee512", "reference": "fc8eff5841b549fbd66f89e1fd7cfb6a823ee512", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Asset Component", "homepage": "https://symfony.com", "time": "2020-04-12T14:39:55+00:00"}, {"name": "symfony/cache", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "ba0aa1738d04df338c0fabdbecf9cf5fddcdb63f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/ba0aa1738d04df338c0fabdbecf9cf5fddcdb63f", "reference": "ba0aa1738d04df338c0fabdbecf9cf5fddcdb63f", "shasum": ""}, "require": {"php": "^7.1.3", "psr/cache": "~1.0", "psr/log": "~1.0", "symfony/cache-contracts": "^1.1.7|^2", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.2|^5.0"}, "conflict": {"doctrine/dbal": "<2.5", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0", "psr/simple-cache-implementation": "1.0", "symfony/cache-implementation": "1.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "~1.6", "doctrine/dbal": "~2.5", "predis/predis": "~1.1", "psr/simple-cache": "^1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.1|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Cache component with PSR-6, PSR-16, and tags", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "time": "2020-04-28T17:55:16+00:00"}, {"name": "symfony/cache-contracts", "version": "v1.1.7", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "af50d14ada9e4e82cfabfabdc502d144f89be0a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/af50d14ada9e4e82cfabfabdc502d144f89be0a1", "reference": "af50d14ada9e4e82cfabfabdc502d144f89be0a1", "shasum": ""}, "require": {"php": "^7.1.3", "psr/cache": "^1.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-10-04T21:43:27+00:00"}, {"name": "symfony/config", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "8ba41fe053683e1e6e3f6fa21f07ea5c4dd9e4c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/8ba41fe053683e1e6e3f6fa21f07ea5c4dd9e4c0", "reference": "8ba41fe053683e1e6e3f6fa21f07ea5c4dd9e4c0", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<3.4"}, "require-dev": {"symfony/event-dispatcher": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/messenger": "^4.1|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2020-04-15T15:56:18+00:00"}, {"name": "symfony/console", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "10bb3ee3c97308869d53b3e3d03f6ac23ff985f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/10bb3ee3c97308869d53b3e3d03f6ac23ff985f7", "reference": "10bb3ee3c97308869d53b3e3d03f6ac23ff985f7", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2020-03-30T11:41:10+00:00"}, {"name": "symfony/debug", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "346636d2cae417992ecfd761979b2ab98b339a45"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/346636d2cae417992ecfd761979b2ab98b339a45", "reference": "346636d2cae417992ecfd761979b2ab98b339a45", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "abandoned": "symfony/error-handler", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/dependency-injection", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "9d0c2807962f7f12264ab459f48fb541dbd386bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/9d0c2807962f7f12264ab459f48fb541dbd386bd", "reference": "9d0c2807962f7f12264ab459f48fb541dbd386bd", "shasum": ""}, "require": {"php": "^7.1.3", "psr/container": "^1.0", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<4.3|>=5.0", "symfony/finder": "<3.4", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0"}, "require-dev": {"symfony/config": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2020-04-16T16:36:56+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "642cb1000331b8dc5568587f60aeb299070f9a55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/642cb1000331b8dc5568587f60aeb299070f9a55", "reference": "642cb1000331b8dc5568587f60aeb299070f9a55", "shasum": ""}, "require": {"doctrine/event-manager": "~1.0", "doctrine/persistence": "^1.3", "php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/form": "<4.4", "symfony/http-kernel": "<4.3.7", "symfony/messenger": "<4.3", "symfony/security-core": "<4.4", "symfony/validator": "<4.4.2|<5.0.2,>=5.0"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.6", "doctrine/collections": "~1.0", "doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.4", "doctrine/orm": "^2.6.3", "doctrine/reflection": "~1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^4.4|^5.0", "symfony/http-kernel": "^4.3.7", "symfony/messenger": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/proxy-manager-bridge": "^3.4|^4.0|^5.0", "symfony/security-core": "^4.4|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/translation": "^3.4|^4.0|^5.0", "symfony/validator": "^4.4.2|^5.0.2", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/data-fixtures": "", "doctrine/dbal": "", "doctrine/orm": "", "symfony/form": "", "symfony/property-info": "", "symfony/validator": ""}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Bridge", "homepage": "https://symfony.com", "time": "2020-04-12T16:45:36+00:00"}, {"name": "symfony/dotenv", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "a78e698cfb8aca8ef6814639eb5ffc17180a4326"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/a78e698cfb8aca8ef6814639eb5ffc17180a4326", "reference": "a78e698cfb8aca8ef6814639eb5ffc17180a4326", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/process": "^3.4.2|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "7e9828fc98aa1cf27b422fe478a84f5b0abb7358"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/7e9828fc98aa1cf27b422fe478a84f5b0abb7358", "reference": "7e9828fc98aa1cf27b422fe478a84f5b0abb7358", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ErrorHandler Component", "homepage": "https://symfony.com", "time": "2020-03-30T14:07:33+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "abc8e3618bfdb55e44c8c6a00abd333f831bbfed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/abc8e3618bfdb55e44c8c6a00abd333f831bbfed", "reference": "abc8e3618bfdb55e44c8c6a00abd333f831bbfed", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.7", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-09-17T09:54:03+00:00"}, {"name": "symfony/expression-language", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "38010d8d1eb425b74f25b87c366c4d97e4b06a89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/38010d8d1eb425b74f25b87c366c4d97e4b06a89", "reference": "38010d8d1eb425b74f25b87c366c4d97e4b06a89", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ExpressionLanguage Component", "homepage": "https://symfony.com", "time": "2020-04-15T15:55:41+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "a3ebf3bfd8a98a147c010a568add5a8aa4edea0f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/a3ebf3bfd8a98a147c010a568add5a8aa4edea0f", "reference": "a3ebf3bfd8a98a147c010a568add5a8aa4edea0f", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2020-04-12T14:39:55+00:00"}, {"name": "symfony/finder", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "5729f943f9854c5781984ed4907bbb817735776b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/5729f943f9854c5781984ed4907bbb817735776b", "reference": "5729f943f9854c5781984ed4907bbb817735776b", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/flex", "version": "v1.21.4", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "7b40eec950ded5de7054f807c209d3c612efe517"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/7b40eec950ded5de7054f807c209d3c612efe517", "reference": "7b40eec950ded5de7054f807c209d3c612efe517", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=7.1"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "symfony/dotenv": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/phpunit-bridge": "^4.4.12|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-02T11:08:17+00:00"}, {"name": "symfony/form", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "505299904397a7c6d515a7c03cdbc1b4a1d4a21f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/505299904397a7c6d515a7c03cdbc1b4a1d4a21f", "reference": "505299904397a7c6d515a7c03cdbc1b4a1d4a21f", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher": "^4.3", "symfony/intl": "^4.4|^5.0", "symfony/options-resolver": "~4.3|^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<4.3", "symfony/dependency-injection": "<3.4", "symfony/doctrine-bridge": "<3.4", "symfony/framework-bundle": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": "<4.2", "symfony/twig-bridge": "<3.4.5|<4.0.5,>=4.0"}, "require-dev": {"doctrine/collections": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^4.3|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/validator": "^3.4.31|^4.3.4|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"symfony/security-csrf": "For protecting forms against CSRF attacks.", "symfony/twig-bridge": "For templating with <PERSON><PERSON>.", "symfony/validator": "For form validation."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Form Component", "homepage": "https://symfony.com", "time": "2020-04-28T17:55:16+00:00"}, {"name": "symfony/framework-bundle", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "fdacdf191a71aef94e05b64319868f4d06fe509c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/fdacdf191a71aef94e05b64319868f4d06fe509c", "reference": "fdacdf191a71aef94e05b64319868f4d06fe509c", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/cache": "^4.4|^5.0", "symfony/config": "^4.3.4|^5.0", "symfony/dependency-injection": "^4.4.1|^5.0.1", "symfony/error-handler": "^4.4.1|^5.0.1", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^4.4|^5.0"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.0", "phpdocumentor/type-resolver": "<0.2.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.4", "symfony/browser-kit": "<4.3", "symfony/console": "<4.3", "symfony/dom-crawler": "<4.3", "symfony/dotenv": "<4.3.6", "symfony/form": "<4.3.5", "symfony/http-client": "<4.4", "symfony/lock": "<4.4", "symfony/mailer": "<4.4", "symfony/messenger": "<4.4", "symfony/mime": "<4.4", "symfony/property-info": "<3.4", "symfony/security-bundle": "<4.4", "symfony/serializer": "<4.4", "symfony/stopwatch": "<3.4", "symfony/translation": "<4.4", "symfony/twig-bridge": "<4.1.1", "symfony/twig-bundle": "<4.4", "symfony/validator": "<4.4", "symfony/web-profiler-bundle": "<4.4", "symfony/workflow": "<4.3.6"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "paragonie/sodium_compat": "^1.8", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/console": "^4.3.4|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^4.3|^5.0", "symfony/dotenv": "^4.3.6|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^4.3.5|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/mailer": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/security-http": "^3.4|^4.0|^5.0", "symfony/serializer": "^4.4|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^4.4|^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^4.3.6|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "time": "2020-04-23T20:17:53+00:00"}, {"name": "symfony/http-client", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "88d1745f4095727b8bf0574a0f414331f4ec229c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/88d1745f4095727b8bf0574a0f414331f4ec229c", "reference": "88d1745f4095727b8bf0574a0f414331f4ec229c", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}, "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/process": "^4.2|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpClient component", "homepage": "https://symfony.com", "time": "2020-04-12T16:14:02+00:00"}, {"name": "symfony/http-client-contracts", "version": "v1.1.8", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "088bae75cfa2ec5eb6d33dce17dbd8613150ce6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/088bae75cfa2ec5eb6d33dce17dbd8613150ce6e", "reference": "088bae75cfa2ec5eb6d33dce17dbd8613150ce6e", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-11-07T12:44:51+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "ec5bd254c223786f5fa2bb49a1e705c1b8e7cee2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/ec5bd254c223786f5fa2bb49a1e705c1b8e7cee2", "reference": "ec5bd254c223786f5fa2bb49a1e705c1b8e7cee2", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2020-04-18T20:40:08+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "1799a6c01f0db5851f399151abdb5d6393fec277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/1799a6c01f0db5851f399151abdb5d6393fec277", "reference": "1799a6c01f0db5851f399151abdb5d6393fec277", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2020-04-28T18:47:42+00:00"}, {"name": "symfony/inflector", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "53cfa47fe9142f39b5605df67bada3893dd4f46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/53cfa47fe9142f39b5605df67bada3893dd4f46c", "reference": "53cfa47fe9142f39b5605df67bada3893dd4f46c", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "abandoned": "EnglishInflector from the String component", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/intl", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "040f10fde20ae35e8623771ba8a733508c87aa6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/040f10fde20ae35e8623771ba8a733508c87aa6a", "reference": "040f10fde20ae35e8623771ba8a733508c87aa6a", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-intl-icu": "~1.0"}, "require-dev": {"symfony/filesystem": "^3.4|^4.0|^5.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A PHP replacement layer for the C intl extension that includes additional data from the ICU library.", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "time": "2020-04-12T14:39:55+00:00"}, {"name": "symfony/mime", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "7a583ffb6c7dd5aabb5db920817a3cc39261c517"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/7a583ffb6c7dd5aabb5db920817a3cc39261c517", "reference": "7a583ffb6c7dd5aabb5db920817a3cc39261c517", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A library to manipulate MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "time": "2020-04-16T14:49:30+00:00"}, {"name": "symfony/monolog-bridge", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "224355f29abfb8b00a4c5fb22bdaff5c47e82105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/224355f29abfb8b00a4c5fb22bdaff5c47e82105", "reference": "224355f29abfb8b00a4c5fb22bdaff5c47e82105", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1", "php": "^7.1.3", "symfony/http-kernel": "^4.3", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/console": "<3.4", "symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/security-core": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Monolog Bridge", "homepage": "https://symfony.com", "time": "2020-04-12T16:14:02+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "dd80460fcfe1fa2050a7103ad818e9d0686ce6fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/dd80460fcfe1fa2050a7103ad818e9d0686ce6fd", "reference": "dd80460fcfe1fa2050a7103ad818e9d0686ce6fd", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^3.4.19 || ^4.0 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "time": "2019-11-13T13:11:14+00:00"}, {"name": "symfony/options-resolver", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "ade3d89dd3b875b83c8cff2980c9bb0daf6ef297"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/ade3d89dd3b875b83c8cff2980c9bb0daf6ef297", "reference": "ade3d89dd3b875b83c8cff2980c9bb0daf6ef297", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2020-04-06T10:16:26+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "4ef3923e4a86e1b6ef72d42be59dbf7d33a685e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/4ef3923e4a86e1b6ef72d42be59dbf7d33a685e3", "reference": "4ef3923e4a86e1b6ef72d42be59dbf7d33a685e3", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/intl": "~2.3|~3.0|~4.0|~5.0"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/3bff59ea7047e925be6b7f2059d60af31bb46d6a", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fa79b11539418b02fc5e1897267673ba2c19419c", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "f048e612a3905f34931127360bdd2def19a5e582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/f048e612a3905f34931127360bdd2def19a5e582", "reference": "f048e612a3905f34931127360bdd2def19a5e582", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a760d8964ff79ab9bf057613a5808284ec852ccc", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/process", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "4b6a9a4013baa65d409153cbb5a895bf093dc7f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/4b6a9a4013baa65d409153cbb5a895bf093dc7f4", "reference": "4b6a9a4013baa65d409153cbb5a895bf093dc7f4", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2020-04-15T15:56:18+00:00"}, {"name": "symfony/property-access", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "f6a51bd76a3a5c36c57221a4f491b9cf02663672"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/f6a51bd76a3a5c36c57221a4f491b9cf02663672", "reference": "f6a51bd76a3a5c36c57221a4f491b9cf02663672", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/cache": "^3.4|^4.0|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "time": "2020-04-15T15:55:41+00:00"}, {"name": "symfony/property-info", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "ab5bb41dee66b4f7b4e0f615772b07d8f466e218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/ab5bb41dee66b4f7b4e0f615772b07d8f466e218", "reference": "ab5bb41dee66b4f7b4e0f615772b07d8f466e218", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "symfony/dependency-injection": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.7", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/serializer": "^3.4|^4.0|^5.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Property Info Component", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "time": "2020-04-06T10:16:26+00:00"}, {"name": "symfony/requirements-checker", "version": "v1.1.5", "source": {"type": "git", "url": "https://github.com/symfony/requirements-checker.git", "reference": "991978163f5a653a17e7b7592157c096c2afd2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/requirements-checker/zipball/991978163f5a653a17e7b7592157c096c2afd2f7", "reference": "991978163f5a653a17e7b7592157c096c2afd2f7", "shasum": ""}, "require": {"php": ">=5.3.9"}, "bin": ["bin/requirements-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Requirements\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Check Symfony requirements and give recommendations", "keywords": ["configuration", "distribution"], "time": "2019-06-25T16:43:49+00:00"}, {"name": "symfony/routing", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "67b4e1f99c050cbc310b8f3d0dbdc4b0212c052c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/67b4e1f99c050cbc310b8f3d0dbdc4b0212c052c", "reference": "67b4e1f99c050cbc310b8f3d0dbdc4b0212c052c", "shasum": ""}, "require": {"php": "^7.1.3"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.2", "psr/log": "~1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2020-04-21T19:59:53+00:00"}, {"name": "symfony/security-bundle", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "dd1641ab03f2dd62e7aa0de8efd80cee20d585ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/dd1641ab03f2dd62e7aa0de8efd80cee20d585ff", "reference": "dd1641ab03f2dd62e7aa0de8efd80cee20d585ff", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/security-core": "^4.4", "symfony/security-csrf": "^4.2|^5.0", "symfony/security-guard": "^4.2|^5.0", "symfony/security-http": "^4.4.5"}, "conflict": {"symfony/browser-kit": "<4.2", "symfony/console": "<3.4", "symfony/framework-bundle": "<4.4", "symfony/ldap": "<4.4", "symfony/twig-bundle": "<4.4"}, "require-dev": {"doctrine/doctrine-bundle": "^1.5|^2.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^4.2|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/serializer": "^4.4|^5.0", "symfony/translation": "^3.4|^4.0|^5.0", "symfony/twig-bridge": "^3.4|^4.0|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony SecurityBundle", "homepage": "https://symfony.com", "time": "2020-04-12T22:16:27+00:00"}, {"name": "symfony/security-core", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "fc84e9481e5bd9d80f70c0d8151601211377a5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/fc84e9481e5bd9d80f70c0d8151601211377a5dc", "reference": "fc84e9481e5bd9d80f70c0d8151601211377a5dc", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1|^2", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/event-dispatcher": "<4.3|>=5", "symfony/ldap": "<4.4", "symfony/security-guard": "<4.3"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/ldap": "^4.4|^5.0", "symfony/validator": "^3.4.31|^4.3.4|^5.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "time": "2020-04-21T21:19:23+00:00"}, {"name": "symfony/security-csrf", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "286a71ff176e1b0dd071f0e73dcec0970a56634b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/286a71ff176e1b0dd071f0e73dcec0970a56634b", "reference": "286a71ff176e1b0dd071f0e73dcec0970a56634b", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/security-core": "^3.4|^4.0|^5.0"}, "conflict": {"symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/http-foundation": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/security-guard", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/security-guard.git", "reference": "d2ba618ed2a52f37dd74fb2c52a14388beddd5fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-guard/zipball/d2ba618ed2a52f37dd74fb2c52a14388beddd5fc", "reference": "d2ba618ed2a52f37dd74fb2c52a14388beddd5fc", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/security-core": "^3.4.22|^4.2.3|^5.0", "symfony/security-http": "^4.4.1"}, "require-dev": {"psr/log": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Guard\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Guard", "homepage": "https://symfony.com", "time": "2020-04-15T15:55:41+00:00"}, {"name": "symfony/security-http", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "055a4f4fe58ab19515fa573919bf7ebd114f4aa7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/055a4f4fe58ab19515fa573919bf7ebd114f4aa7", "reference": "055a4f4fe58ab19515fa573919bf7ebd114f4aa7", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/http-foundation": "^3.4.40|^4.4.7|^5.0.7", "symfony/http-kernel": "^4.4", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/security-core": "^4.4.8"}, "conflict": {"symfony/event-dispatcher": ">=5", "symfony/security-csrf": "<3.4.11|~4.0,<4.0.11"}, "require-dev": {"psr/log": "~1.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/security-csrf": "^3.4.11|^4.0.11|^5.0"}, "suggest": {"symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/security-csrf": "For using tokens to protect authentication/logout attempts"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "time": "2020-04-03T17:46:33+00:00"}, {"name": "symfony/serializer", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "067b6a064ffc53b48d3854c7b408b1ea26017a50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/067b6a064ffc53b48d3854c7b408b1ea26017a50", "reference": "067b6a064ffc53b48d3854c7b408b1ea26017a50", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.4", "symfony/property-access": "<3.4", "symfony/property-info": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.2|^4.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4.13|~4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "time": "2020-04-12T16:14:02+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.8", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "ffc7f5692092df31515df2a5ecf3b7302b3ddacf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/ffc7f5692092df31515df2a5ecf3b7302b3ddacf", "reference": "ffc7f5692092df31515df2a5ecf3b7302b3ddacf", "shasum": ""}, "require": {"php": "^7.1.3", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-10-14T12:27:06+00:00"}, {"name": "symfony/stopwatch", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "e0324d3560e4128270e3f08617480d9233d81cfc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/e0324d3560e4128270e3f08617480d9233d81cfc", "reference": "e0324d3560e4128270e3f08617480d9233d81cfc", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/service-contracts": "^1.0|^2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "553d2474288349faed873da8ab7c1551a00d26ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/553d2474288349faed873da8ab7c1551a00d26ae", "reference": "553d2474288349faed873da8ab7c1551a00d26ae", "shasum": ""}, "require": {"php": ">=7.1", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^4.3.8|^5.0", "symfony/dependency-injection": "^4.3.8|^5.0", "symfony/http-kernel": "^4.3.8|^5.0"}, "conflict": {"twig/twig": "<1.41|<2.10"}, "require-dev": {"symfony/console": "^4.3.8|^5.0", "symfony/framework-bundle": "^4.3.8|^5.0", "symfony/phpunit-bridge": "^4.3.8|^5.0", "symfony/yaml": "^4.3.8|^5.0"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "abandoned": "symfony/mailer", "time": "2019-11-14T16:18:31+00:00"}, {"name": "symfony/templating", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/templating.git", "reference": "d65ba8cd13339b692f709ea36625851de218c4dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/templating/zipball/d65ba8cd13339b692f709ea36625851de218c4dc", "reference": "d65ba8cd13339b692f709ea36625851de218c4dc", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "require-dev": {"psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "For using debug logging in loaders"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Templating\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Templating Component", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/translation", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "8272bbd2b7e220ef812eba2a2b30068a5c64b191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/8272bbd2b7e220ef812eba2a2b30068a5c64b191", "reference": "8272bbd2b7e220ef812eba2a2b30068a5c64b191", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2020-04-12T16:45:36+00:00"}, {"name": "symfony/translation-contracts", "version": "v1.1.7", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "364518c132c95642e530d9b2d217acbc2ccac3e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/364518c132c95642e530d9b2d217acbc2ccac3e6", "reference": "364518c132c95642e530d9b2d217acbc2ccac3e6", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-09-17T11:12:18+00:00"}, {"name": "symfony/twig-bridge", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "d64035d0d6b3dbeed3a6839e3833779aaecf3513"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/d64035d0d6b3dbeed3a6839e3833779aaecf3513", "reference": "d64035d0d6b3dbeed3a6839e3833779aaecf3513", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.41|^2.10|^3.0"}, "conflict": {"symfony/console": "<3.4", "symfony/form": "<4.4", "symfony/http-foundation": "<4.3", "symfony/translation": "<4.2", "symfony/workflow": "<4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/form": "^4.3.5", "symfony/http-foundation": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^3.0|^4.0|^5.0", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/security-http": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2.1|^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^4.3|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/cssinliner-extra": "^2.12", "twig/inky-extra": "^2.12", "twig/markdown-extra": "^2.12"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security-core": "For using the SecurityExtension", "symfony/security-csrf": "For using the CsrfExtension", "symfony/security-http": "For using the LogoutUrlExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Twig Bridge", "homepage": "https://symfony.com", "time": "2020-04-14T09:16:32+00:00"}, {"name": "symfony/twig-bundle", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "79046e5189c5f4da923f395ccc11db930953c990"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/79046e5189c5f4da923f395ccc11db930953c990", "reference": "79046e5189c5f4da923f395ccc11db930953c990", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/http-foundation": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "^4.4|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "conflict": {"symfony/dependency-injection": "<4.1", "symfony/framework-bundle": "<4.4", "symfony/translation": "<4.2"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.2.5|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/web-link": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony TwigBundle", "homepage": "https://symfony.com", "time": "2020-04-15T15:55:41+00:00"}, {"name": "symfony/validator", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "1780dff34d756f924ed7bb4f1cd94a7f9685eb69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/1780dff34d756f924ed7bb4f1cd94a7f9685eb69", "reference": "1780dff34d756f924ed7bb4f1cd94a7f9685eb69", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1|^2"}, "conflict": {"doctrine/lexer": "<1.0.2", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": ">=5.0", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "egulias/email-validator": "^2.1.10", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/http-foundation": "^4.1|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^4.3|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "time": "2020-04-28T18:23:58+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "c587e04ce5d1aa62d534a038f574d9a709e814cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/c587e04ce5d1aa62d534a038f574d9a709e814cf", "reference": "c587e04ce5d1aa62d534a038f574d9a709e814cf", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2020-04-12T16:14:02+00:00"}, {"name": "symfony/var-exporter", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "6e95bdca4a4604da6c148729972d4b627a034b13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/6e95bdca4a4604da6c148729972d4b627a034b13", "reference": "6e95bdca4a4604da6c148729972d4b627a034b13", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/var-dumper": "^4.1.1|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A blend of var_export() + serialize() to turn any serializable data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "time": "2020-04-15T15:55:41+00:00"}, {"name": "symfony/web-link", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/web-link.git", "reference": "9ec692b342855335f3f4e77753ad71f85c6038f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-link/zipball/9ec692b342855335f3f4e77753ad71f85c6038f8", "reference": "9ec692b342855335f3f4e77753ad71f85c6038f8", "shasum": ""}, "require": {"php": "^7.1.3", "psr/link": "^1.0", "symfony/polyfill-php72": "^1.5"}, "conflict": {"symfony/http-kernel": "<4.3"}, "provide": {"psr/link-implementation": "1.0"}, "require-dev": {"symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.3|^5.0"}, "suggest": {"symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\WebLink\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebLink Component", "homepage": "https://symfony.com", "keywords": ["dns-prefetch", "http", "http2", "link", "performance", "prefetch", "preload", "prerender", "psr13", "push"], "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/webpack-encore-bundle", "version": "v1.7.3", "source": {"type": "git", "url": "https://github.com/symfony/webpack-encore-bundle.git", "reference": "5c0f659eceae87271cce54bbdfb05ed8ec9007bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/webpack-encore-bundle/zipball/5c0f659eceae87271cce54bbdfb05ed8ec9007bd", "reference": "5c0f659eceae87271cce54bbdfb05ed8ec9007bd", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/asset": "^3.4 || ^4.0 || ^5.0", "symfony/config": "^3.4 || ^4.0 || ^5.0", "symfony/dependency-injection": "^3.4 || ^4.0 || ^5.0", "symfony/http-kernel": "^3.4 || ^4.0 || ^5.0", "symfony/service-contracts": "^1.0 || ^2.0"}, "require-dev": {"symfony/framework-bundle": "^3.4 || ^4.0 || ^5.0", "symfony/phpunit-bridge": "^4.3.5 || ^5.0", "symfony/twig-bundle": "^3.4 || ^4.0 || ^5.0", "symfony/web-link": "^3.4 || ^4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"thanks": {"name": "symfony/webpack-encore", "url": "https://github.com/symfony/webpack-encore"}}, "autoload": {"psr-4": {"Symfony\\WebpackEncoreBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Integration with your Symfony app & Webpack Encore!", "time": "2020-01-31T15:31:59+00:00"}, {"name": "symfony/yaml", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "b385dce1c0e9f839b384af90188638819433e252"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/b385dce1c0e9f839b384af90188638819433e252", "reference": "b385dce1c0e9f839b384af90188638819433e252", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2020-04-28T17:55:16+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "abandoned": true, "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v2.12.5", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "18772e0190734944277ee97a02a9a6c6555fcd94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/18772e0190734944277ee97a02a9a6c6555fcd94", "reference": "18772e0190734944277ee97a02a9a6c6555fcd94", "shasum": ""}, "require": {"php": "^7.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.12-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2020-02-11T15:31:23+00:00"}, {"name": "vich/uploader-bundle", "version": "1.8.8", "source": {"type": "git", "url": "https://github.com/dustin10/VichUploaderBundle.git", "reference": "759bf0e6d8ceb4bcfb066da9b2789ab3dfea0d9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dustin10/VichUploaderBundle/zipball/759bf0e6d8ceb4bcfb066da9b2789ab3dfea0d9c", "reference": "759bf0e6d8ceb4bcfb066da9b2789ab3dfea0d9c", "shasum": ""}, "require": {"behat/transliterator": "^1.2", "jms/metadata": "^1.6", "php": "^7.1", "symfony/config": "^3.4|^4.0", "symfony/dependency-injection": "^3.4|^4.0", "symfony/event-dispatcher": "^3.4|^4.0", "symfony/form": "^3.4|^4.0", "symfony/http-foundation": "^3.4|^4.0", "symfony/http-kernel": "^3.4|^4.0", "symfony/property-access": "^3.4|^4.0", "symfony/templating": "^3.4|^4.0"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/doctrine-bundle": "^1.8", "doctrine/mongodb-odm": "^1.2", "doctrine/orm": "^2.5", "ext-sqlite3": "*", "knplabs/knp-gaufrette-bundle": "^0.5", "matthiasnoback/symfony-dependency-injection-test": "^2.3|^3.0", "mikey179/vfsstream": "^1.6", "oneup/flysystem-bundle": "^3.0", "phpunit/phpunit": "^6.5|^7.0", "symfony/browser-kit": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0", "symfony/doctrine-bridge": "^3.4|^4.0", "symfony/dom-crawler": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.3", "symfony/security-csrf": "^3.4|^4.0", "symfony/translation": "^3.4|^4.0", "symfony/twig-bridge": "^3.4|^4.0", "symfony/twig-bundle": "^3.4|^4.0", "symfony/validator": "^3.4|^4.0", "symfony/var-dumper": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "suggest": {"doctrine/doctrine-bundle": "For integration with Doctrine", "doctrine/mongodb-odm-bundle": "For integration with Doctrine ODM", "doctrine/orm": "For integration with Doctrine ORM", "doctrine/phpcr-odm": "For integration with Doctrine PHPCR", "knplabs/knp-gaufrette-bundle": "For integration with Gaufrette", "liip/imagine-bundle": "To generate image thumbnails", "ocramius/proxy-manager": "To use lazy services", "oneup/flysystem-bundle": "For integration with Flysystem", "symfony/yaml": "To use YAML mapping", "willdurand/propel-eventdispatcher-bundle": "For integration with Propel"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"Vich\\UploaderBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "d<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Ease file uploads attached to entities", "homepage": "https://github.com/dustin10/VichUploaderBundle", "keywords": ["file uploads", "upload"], "time": "2019-04-28T17:16:03+00:00"}, {"name": "webmozart/assert", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/ab2cb0b3b559010b75981b1bdce728da3ee90ad6", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-04-18T12:12:48+00:00"}, {"name": "welp/mailchimp-bundle", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/welpdev/mailchimp-bundle.git", "reference": "6065060750cf903b1d45865fdafa848bf70f6637"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/welpdev/mailchimp-bundle/zipball/6065060750cf903b1d45865fdafa848bf70f6637", "reference": "6065060750cf903b1d45865fdafa848bf70f6637", "shasum": ""}, "require": {"drewm/mailchimp-api": "2.5.*", "php": ">=5.6"}, "require-dev": {"phpspec/phpspec": "~2@dev", "phpunit/phpunit": "~5", "symfony/symfony": ">=2.7"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Welp\\MailchimpBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "MailChimp API V3 Symfony Bundle", "homepage": "https://github.com/welpdev/mailchimp-bundle", "keywords": ["api", "mailchimp", "subscriber", "symfony", "user", "v3"], "time": "2019-10-21T06:33:33+00:00"}, {"name": "will<PERSON><PERSON>/js-translation-bundle", "version": "2.6.6", "source": {"type": "git", "url": "https://github.com/willdurand/BazingaJsTranslationBundle.git", "reference": "9c80406dd4cc195f1f835a52e038fb80a96563b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/BazingaJsTranslationBundle/zipball/9c80406dd4cc195f1f835a52e038fb80a96563b2", "reference": "9c80406dd4cc195f1f835a52e038fb80a96563b2", "shasum": ""}, "require": {"symfony/console": "~2.7|~3.1|~4.0", "symfony/finder": "~2.7|~3.1|~4.0", "symfony/framework-bundle": "~2.7|~3.1|~4.0", "symfony/intl": "~2.7|~3.1|~4.0", "symfony/translation": "~2.7|~3.1|~4.0", "symfony/twig-bundle": "~2.7|~3.1|~4.0"}, "replace": {"willdurand/expose-translation-bundle": "2.5.*"}, "require-dev": {"phpunit/phpunit": "^4.8|~5.7", "symfony/asset": "~2.7|~3.1|~4.0", "symfony/browser-kit": "~2.7|~3.1|~4.0", "symfony/phpunit-bridge": "~2.7|~3.1|~4.0", "symfony/twig-bundle": "~2.7|~3.1|~4.0", "symfony/yaml": "~2.7|~3.1|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Bazinga\\Bundle\\JsTranslationBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A pretty nice way to expose your translation messages to your JavaScript.", "keywords": ["javascript", "symfony", "translation"], "time": "2018-02-11T14:19:14+00:00"}, {"name": "<PERSON><PERSON><PERSON>/jsonp-callback-validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/JsonpCallbackValidator.git", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/JsonpCallbackValidator/zipball/1a7d388bb521959e612ef50c5c7b1691b097e909", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"JsonpCallbackValidator": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.willdurand.fr"}], "description": "JSONP callback validator.", "time": "2014-01-20T22:35:06+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "03436ededa67c6e83b9b12defac15384cb399dc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/03436ededa67c6e83b9b12defac15384cb399dc9", "reference": "03436ededa67c6e83b9b12defac15384cb399dc9", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "time": "2017-05-14T17:21:12+00:00"}, {"name": "yellowskies/qr-code-bundle", "version": "1.2.8", "source": {"type": "git", "url": "https://github.com/AntoineTesson/QRcodeBundle.git", "reference": "187c1c82e0d79c5f0ea47fb356c5447cb6409a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/AntoineTesson/QRcodeBundle/zipball/187c1c82e0d79c5f0ea47fb356c5447cb6409a54", "reference": "187c1c82e0d79c5f0ea47fb356c5447cb6409a54", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/framework-bundle": ">=2.3", "symfony/options-resolver": ">=2.1", "symfony/twig-bundle": ">=2.3"}, "require-dev": {"phpunit/phpunit": ">=4.5.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Skies\\QRcodeBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://yellow-skies.com"}], "description": "Symfony2Barcode Generator Bundle with Twig function extension", "keywords": ["Symfony2", "barcode", "bundle", "generator", "qr code", "qrcode", "symfony", "symfony3", "symfony4", "twig"], "time": "2020-03-18T20:35:59+00:00"}, {"name": "zendframework/zend-code", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-code.git", "reference": "268040548f92c2bfcba164421c1add2ba43abaaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-code/zipball/268040548f92c2bfcba164421c1add2ba43abaaa", "reference": "268040548f92c2bfcba164421c1add2ba43abaaa", "shasum": ""}, "require": {"php": "^7.1", "zendframework/zend-eventmanager": "^2.6 || ^3.0"}, "conflict": {"phpspec/prophecy": "<1.9.0"}, "require-dev": {"doctrine/annotations": "^1.7", "ext-phar": "*", "phpunit/phpunit": "^7.5.16 || ^8.4", "zendframework/zend-coding-standard": "^1.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "zendframework/zend-stdlib": "Zend\\Stdlib component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev", "dev-develop": "3.5.x-dev", "dev-dev-4.0": "4.0.x-dev"}}, "autoload": {"psr-4": {"Zend\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "keywords": ["ZendFramework", "code", "zf"], "abandoned": "laminas/laminas-code", "time": "2019-12-10T19:21:15+00:00"}, {"name": "zendframework/zend-eventmanager", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "a5e2583a211f73604691586b8406ff7296a946dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/a5e2583a211f73604691586b8406ff7296a946dd", "reference": "a5e2583a211f73604691586b8406ff7296a946dd", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"athletic/athletic": "^0.1", "container-interop/container-interop": "^1.1.0", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-stdlib": "^2.7.3 || ^3.0"}, "suggest": {"container-interop/container-interop": "^1.1.0, to use the lazy listeners feature", "zendframework/zend-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev", "dev-develop": "3.3-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["event", "eventmanager", "events", "zf2"], "abandoned": "laminas/laminas-eventmanager", "time": "2018-04-25T15:33:34+00:00"}], "packages-dev": [{"name": "doctrine/data-fixtures", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "f0ee99c64922fc3f863715232b615c478a61b0a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/f0ee99c64922fc3f863715232b615c478a61b0a3", "reference": "f0ee99c64922fc3f863715232b615c478a61b0a3", "shasum": ""}, "require": {"doctrine/common": "~2.2", "php": "^7.1"}, "conflict": {"doctrine/phpcr-odm": "<1.3.0"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/dbal": "^2.5.4", "doctrine/mongodb-odm": "^1.3.0", "doctrine/orm": "^2.5.4", "phpunit/phpunit": "^7.0"}, "suggest": {"alcaeus/mongo-php-adapter": "For using MongoDB ODM with PHP 7", "doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\DataFixtures\\": "lib/Doctrine/Common/DataFixtures"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "http://www.doctrine-project.org", "keywords": ["database"], "time": "2019-10-24T04:52:28+00:00"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "39defca57ee0949e1475c46177b30b6d1b732e8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/39defca57ee0949e1475c46177b30b6d1b732e8f", "reference": "39defca57ee0949e1475c46177b30b6d1b732e8f", "shasum": ""}, "require": {"doctrine/data-fixtures": "^1.3", "doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.6.0", "doctrine/persistence": "^1.3", "php": "^7.1", "symfony/config": "^3.4|^4.3|^5.0", "symfony/console": "^3.4|^4.3|^5.0", "symfony/dependency-injection": "^3.4|^4.3|^5.0", "symfony/doctrine-bridge": "^3.4|^4.1|^5.0", "symfony/http-kernel": "^3.4|^4.3|^5.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.4", "symfony/phpunit-bridge": "^4.1|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "time": "2020-04-02T16:40:37+00:00"}, {"name": "symfony/browser-kit", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "e4b0dc1b100bf75b5717c5b451397f230a618a42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/e4b0dc1b100bf75b5717c5b451397f230a618a42", "reference": "e4b0dc1b100bf75b5717c5b451397f230a618a42", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/dom-crawler": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/mime": "^4.3|^5.0", "symfony/process": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2020-03-28T10:15:50+00:00"}, {"name": "symfony/css-selector", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "afc26133a6fbdd4f8842e38893e0ee4685c7c94b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/afc26133a6fbdd4f8842e38893e0ee4685c7c94b", "reference": "afc26133a6fbdd4f8842e38893e0ee4685c7c94b", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/debug-bundle", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/debug-bundle.git", "reference": "dc847e4971b9f76b30e02d421b303d349d5aeed2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-bundle/zipball/dc847e4971b9f76b30e02d421b303d349d5aeed2", "reference": "dc847e4971b9f76b30e02d421b303d349d5aeed2", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/http-kernel": "^3.4|^4.0|^5.0", "symfony/twig-bridge": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.1.1|^5.0"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4"}, "require-dev": {"symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/web-profiler-bundle": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/config": "For service container configuration", "symfony/dependency-injection": "For using as a service from the container"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\DebugBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DebugBundle", "homepage": "https://symfony.com", "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "4d0fb3374324071ecdd94898367a3fa4b5563162"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/4d0fb3374324071ecdd94898367a3fa4b5563162", "reference": "4d0fb3374324071ecdd94898367a3fa4b5563162", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2020-03-29T19:12:22+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "b38c75be880b152ab55cef6cd52bf882d2b6518e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/b38c75be880b152ab55cef6cd52bf882d2b6518e", "reference": "b38c75be880b152ab55cef6cd52bf882d2b6518e", "shasum": ""}, "require": {"doctrine/inflector": "^1.2", "nikic/php-parser": "^4.0", "php": "^7.1.3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "require-dev": {"doctrine/doctrine-bundle": "^1.8|^2.0", "doctrine/orm": "^2.3", "friendsofphp/php-cs-fixer": "^2.8", "friendsoftwig/twigcs": "^3.1.2", "symfony/http-client": "^4.3|^5.0", "symfony/phpunit-bridge": "^4.3|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/security-core": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "time": "2020-05-15T18:51:23+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v5.0.8", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "00b8da18a52fa842b7a39613fb0a63720a354e74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/00b8da18a52fa842b7a39613fb0a63720a354e74", "reference": "00b8da18a52fa842b7a39613fb0a63720a354e74", "shasum": ""}, "require": {"php": ">=5.5.9"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0|<6.4,>=6.0|9.1.2"}, "suggest": {"symfony/error-handler": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "5.0-dev"}, "thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PHPUnit Bridge", "homepage": "https://symfony.com", "time": "2020-04-28T17:58:55+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "aaeaa6a620e0187ea3107bdd4030a8b284f7e89d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/aaeaa6a620e0187ea3107bdd4030a8b284f7e89d", "reference": "aaeaa6a620e0187ea3107bdd4030a8b284f7e89d", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "^4.2|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/routing": "^4.3|^5.0", "symfony/twig-bundle": "^4.2|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "conflict": {"symfony/form": "<4.3", "symfony/messenger": "<4.2"}, "require-dev": {"symfony/browser-kit": "^4.3|^5.0", "symfony/console": "^4.3|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebProfilerBundle", "homepage": "https://symfony.com", "time": "2020-04-28T17:55:16+00:00"}, {"name": "symfony/web-server-bundle", "version": "v4.3.7", "source": {"type": "git", "url": "https://github.com/symfony/web-server-bundle.git", "reference": "dc26b980900ddf3e9feade14e5b21c029e8ca92f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-server-bundle/zipball/dc26b980900ddf3e9feade14e5b21c029e8ca92f", "reference": "dc26b980900ddf3e9feade14e5b21c029e8ca92f", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/process": "^3.4.2|^4.0.2"}, "suggest": {"symfony/expression-language": "For using the filter option of the log server.", "symfony/monolog-bridge": "For using the log server."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\WebServerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebServerBundle", "homepage": "https://symfony.com", "abandoned": true, "time": "2019-08-20T14:27:59+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"coresphere/console-bundle": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.1.3", "ext-ctype": "*", "ext-iconv": "*"}, "platform-dev": [], "plugin-api-version": "1.1.0"}