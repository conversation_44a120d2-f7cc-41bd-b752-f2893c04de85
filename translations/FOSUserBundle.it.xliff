<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:jms="urn:jms:translation" version="1.2">
  <file date="2022-12-08T08:25:42Z" source-language="en" target-language="it" datatype="plaintext" original="not.available">
    <header>
      <tool tool-id="JMSTranslationBundle" tool-name="JMSTranslationBundle" tool-version="1.1.0-DEV"/>
      <note>The source node in most cases contains the sample message as written by the developer. If it looks like a dot-delimitted string such as "form.label.firstname", then the developer has not provided a default message.</note>
    </header>
    <body>
      <trans-unit id="8be3c943b1609fffbfc51aad666d0a04adf83c9d" resname="Password">
        <source>Password</source>
        <target state="new">Parola d'ordine</target>
        <jms:reference-file line="53">\..\..\..\..\..\C:/wamp64/www/scd_eve/src\Form\RegistrationType.php\</jms:reference-file>
      </trans-unit>
      <trans-unit id="7ceacdcaab4c898f58352ac7759cc808a85290da" resname="Privacy policy">
        <source>Privacy policy</source>
        <target state="new">Politica sulla riservatezza</target>
        <jms:reference-file line="93">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\register-attendee.html.twig\</jms:reference-file>
        <jms:reference-file line="88">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\register-organizer.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="a6a1d25b018c9318540556e3fe374fdf8b23aff8" resname="Terms of service">
        <source>Terms of service</source>
        <target state="new">Termini di servizio</target>
        <jms:reference-file line="89">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\register-attendee.html.twig\</jms:reference-file>
        <jms:reference-file line="84">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\register-organizer.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="ca4f9dcf204e2037bfe5884867bead98bd9cbaf8" resname="Welcome">
        <source>Welcome</source>
        <target state="new">Benvenuto</target>
        <jms:reference-file line="21">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\email.html.twig\</jms:reference-file>
        <jms:reference-file line="20">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="a5eba05921c11add89e9b68570149da22499c604" resname="change_password.submit">
        <source>change_password.submit</source>
        <target state="new">Cambia la password</target>
        <jms:reference-file line="26">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\ChangePassword\change_password.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="1fa5b2873fe30be026b854f6a51503b86c6ac81a" resname="registration.back">
        <source>registration.back</source>
        <target state="new">Indietro</target>
        <jms:reference-file line="20">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\confirmed.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="85a4eff0132a6f92eae3057837f285b3aedf38b8" resname="registration.check_email">
        <source>registration.check_email</source>
        <target state="new">È stata inviata un'e-mail a %email%. Contiene un link di attivazione che devi cliccare per attivare il tuo account.</target>
        <jms:reference-file line="18">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\check_email.html.twig\</jms:reference-file>
        <jms:reference-file line="5">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\check_email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="7a0a8fbc70fe82997f8a2ff89090f523ee63f72a" resname="registration.confirmed">
        <source>registration.confirmed</source>
        <target state="new">Congratulazioni %username%, il tuo account è ora attivato.</target>
        <jms:reference-file line="18">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\confirmed.html.twig\</jms:reference-file>
        <jms:reference-file line="5">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\confirmed.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="6362bc7b7a8f72b782ffd590744ab069327bbe4a" resname="registration.email.message">
        <source>registration.email.message</source>
        <target state="new">Ciao %username%!

Per completare l'attivazione del tuo account, fai clic sul seguente link %confirmationUrl%

Questo collegamento può essere utilizzato solo una volta per convalidare il tuo account.

Saluti,
Il gruppo.</target>
        <jms:reference-file line="11">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="10df4e23ec43b671aafea0a3a9ebc8dcc92b1bd0" resname="registration.email.subject">
        <source>registration.email.subject</source>
        <target state="new">Benvenuto %nomeutente%!</target>
        <jms:reference-file line="5">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Registration\email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="40c1a483571045e24d606bf00361cfd4d021ba05" resname="resetting.check_email">
        <source>resetting.check_email</source>
        <target state="new">Una email è stata inviata. Contiene un collegamento su cui è necessario fare clic per reimpostare la password.
Nota: puoi richiedere una nuova password solo una volta entro %tokenLifetime% ore.

Se non ricevi un'e-mail, controlla la cartella della posta indesiderata o riprova.</target>
        <jms:reference-file line="20">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\check_email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="70c8836a0e3b8dadb494b8de56ebed18939359b5" resname="resetting.email.message">
        <source>resetting.email.message</source>
        <target state="new">Ciao %username%!

Per reimpostare la tua password, visita %confirmationUrl%

Saluti,
Il gruppo.</target>
        <jms:reference-file line="10">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="126cf13058808d8948cc673c7933c6c1ce27841d" resname="resetting.email.subject">
        <source>resetting.email.subject</source>
        <target state="new">Resetta la password</target>
        <jms:reference-file line="4">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\email.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="e1470b3c202d9a4fa8e1afa7ed8695917f060ccd" resname="resetting.request.submit">
        <source>resetting.request.submit</source>
        <target state="new">Resetta la password</target>
        <jms:reference-file line="27">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\request.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="7ad41bf8fb30013eb0f31ca027ca8d1dc7a3d96f" resname="resetting.request.username">
        <source>resetting.request.username</source>
        <target state="new">Username o Indirizzo Email</target>
        <jms:reference-file line="24">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\request.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="921fd7a5cc7f9e46eab31822855a22d9808b0477" resname="resetting.reset.submit">
        <source>resetting.reset.submit</source>
        <target state="new">Cambia la password</target>
        <jms:reference-file line="23">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Resetting\reset.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="62d5564c383b885ff0de25abd24a41080499970b" resname="security.login.password">
        <source>security.login.password</source>
        <target state="new">Password</target>
        <jms:reference-file line="53">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Security\login.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="d074ed9a0eb75b7f17ef2274c21d4c3d12d91634" resname="security.login.remember_me">
        <source>security.login.remember_me</source>
        <target state="new">Ricordati di me</target>
        <jms:reference-file line="59">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Security\login.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="64e1b2aaf6156a7e34a13fbc38801e89478efff8" resname="security.login.username">
        <source>security.login.username</source>
        <target state="new">Nome utente</target>
        <jms:reference-file line="44">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSUserBundle\Security\login.html.twig\</jms:reference-file>
      </trans-unit>
    </body>
  </file>
</xliff>
