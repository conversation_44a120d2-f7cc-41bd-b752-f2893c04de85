<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:jms="urn:jms:translation" version="1.2">
  <file date="2022-12-08T08:25:42Z" source-language="en" target-language="it" datatype="plaintext" original="not.available">
    <header>
      <tool tool-id="JMSTranslationBundle" tool-name="JMSTranslationBundle" tool-version="1.1.0-DEV"/>
      <note>The source node in most cases contains the sample message as written by the developer. If it looks like a dot-delimitted string such as "form.label.firstname", then the developer has not provided a default message.</note>
    </header>
    <body>
      <trans-unit id="16a0363b72ae1ad7c9f01a8c054dac4707846866" resname="fos_comment_comment_delete">
        <source>fos_comment_comment_delete</source>
        <target state="new">Elimina</target>
        <jms:reference-file line="48">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_content.html.twig\</jms:reference-file>
        <jms:reference-file line="25">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_remove.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="d89719e2727c0a236034310f351fd3c0f8093ba4" resname="fos_comment_comment_deleted">
        <source>fos_comment_comment_deleted</source>
        <target state="new">Commento cancellato</target>
        <jms:reference-file line="25">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_content.html.twig\</jms:reference-file>
        <jms:reference-file line="75">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="e8e49cff45caf72b4ad2144c2ba1e5fa27a81405" resname="fos_comment_comment_edit">
        <source>fos_comment_comment_edit</source>
        <target state="new">Modificare</target>
        <jms:reference-file line="37">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="22b1cd34a41b8edd8d90482d4ca37d6cda21a94e" resname="fos_comment_comment_edit_cancel">
        <source>fos_comment_comment_edit_cancel</source>
        <target state="new">Annulla</target>
        <jms:reference-file line="25">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_edit.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="d4cb507dd8b6aa71b7e17170136c68df503c62e6" resname="fos_comment_comment_edit_submit">
        <source>fos_comment_comment_edit_submit</source>
        <target state="new">Salva</target>
        <jms:reference-file line="27">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_edit.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="3038326f718094393f1658f4427640624076e71a" resname="fos_comment_comment_new_headline">
        <source>fos_comment_comment_new_headline</source>
        <target state="new">Nuovo commento</target>
        <jms:reference-file line="29">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_new_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="04cadb9c51fb28689900f53ae7f73cba81578339" resname="fos_comment_comment_new_headline_first">
        <source>fos_comment_comment_new_headline_first</source>
        <target state="new">Primo commento</target>
        <jms:reference-file line="25">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_new_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="2dabf6cb9170453335393f8c8d1aadf4d50a44a1" resname="fos_comment_comment_new_submit">
        <source>fos_comment_comment_new_submit</source>
        <target state="new">Salva</target>
        <jms:reference-file line="51">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_new_content.html.twig\</jms:reference-file>
        <jms:reference-file line="27">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\new.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="b22d10c53425dfeb602826776ececc6c62f6db11" resname="fos_comment_comment_reply_cancel">
        <source>fos_comment_comment_reply_cancel</source>
        <target state="new">Annulla</target>
        <jms:reference-file line="49">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_new_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="8b2e5ea9367347779c0a348b3977ef4a21298183" resname="fos_comment_comment_reply_reply_to">
        <source>fos_comment_comment_reply_reply_to</source>
        <target state="new">Rispondere</target>
        <jms:reference-file line="27">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_new_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="45b7f6da89202063b7da7a9f1aadd09fa447f5c2" resname="fos_comment_comment_show_reply">
        <source>fos_comment_comment_show_reply</source>
        <target state="new">Mostrare</target>
        <jms:reference-file line="93">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="574295ccdcdeb9d77da057555a03a508c5cd807f" resname="fos_comment_comment_undelete">
        <source>fos_comment_comment_undelete</source>
        <target state="new">Ristabilire</target>
        <jms:reference-file line="45">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_content.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="e1a4ff92afb95a06b3e7cc09f2492718ee47d811" resname="fos_comment_comment_vote_score">
        <source>fos_comment_comment_vote_score</source>
        <target state="new">Punto</target>
        <jms:reference-file line="14">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comment_votes.html.twig\</jms:reference-file>
      </trans-unit>
      <trans-unit id="da880d94f1eb7c30da98476b3d403893fda43ce4" resname="fos_comment_thread_comment_count">
        <source>fos_comment_thread_comment_count</source>
        <target state="new">I commenti contano</target>
        <jms:reference-file line="37">\..\..\..\..\..\C:/wamp64/www/scd_eve/templates\bundles\FOSCommentBundle\Thread\comments.html.twig\</jms:reference-file>
      </trans-unit>
    </body>
  </file>
</xliff>
