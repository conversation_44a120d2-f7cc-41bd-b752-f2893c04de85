<?php

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use App\Entity\Country;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints as Assert;
use App\Service\AppServices;

class CheckoutType extends AbstractType {

    private $services;

    public function __construct(AppServices $services) {
        $this->services = $services;
    }

    public function buildForm(FormBuilderInterface $builder, array $options) {
        $builder
                ->add('orderReference', HiddenType::class, [
                    'required' => true,
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee', 'pos']])
                    ),
                ])
                ->add('firstname', TextType::class, [
                    'purify_html' => true,
                    'required' => true,
                    'label' => 'First name',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']]),
                        new Length([
                            'min' => 2,
                            'max' => 20,
                            'groups' => ['attendee', 'pos']])
                    ),
                ])
                ->add('lastname', TextType::class, [
                    'purify_html' => true,
                    'required' => true,
                    'label' => 'Last name',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']]),
                        new Length([
                            'min' => 2,
                            'max' => 20,
                            'groups' => ['attendee', 'pos']])
                    ),
                ])
                ->add('email', EmailType::class, [
                    'purify_html' => true,
                    'required' => true,
                    'label' => 'Email',
                    'constraints' => array(
                        new Assert\Email(['groups' => ['attendee', 'guest']]),
                        new NotBlank(['groups' => ['attendee', 'guest']]),
                        new Length([
                            'min' => 2,
                            'max' => 50,
                            'groups' => ['attendee', 'guest']])
                    ),
                ])
                ->add('phone', TextType::class, [
                    'purify_html' => true,
                    'required' => true,
                    'label' => 'Phone number',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee', 'guest']]),
                        new Length([
                            'min' => 5,
                            'max' => 20,
                            'groups' => ['attendee', 'guest']])
                    ),
                ])
                ->add('country', EntityType::class, [
                    'required' => false,
                    'class' => Country::class,
                    'choice_label' => 'name',
                    'label' => 'Country',
                    'attr' => ['class' => 'select2'],
                    'placeholder' => 'Select an option',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']])
                    ),
                    'query_builder' => function () {
                        return $this->services->getCountries(array());
                    },
                ])
                ->add('state', TextType::class, [
                    'purify_html' => true,
                    'required' => false,
                    'label' => 'State',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']]),
                        new Length([
                            'min' => 2,
                            'max' => 50,
                            'groups' => ['attendee']])
                    ),
                ])
                ->add('city', TextType::class, [
                    'purify_html' => true,
                    'required' => false,
                    'label' => 'City',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']]),
                        new Length([
                            'min' => 2,
                            'max' => 50,
                            'groups' => ['attendee']])
                    ),
                ])
                ->add('postalcode', TextType::class, [
                    'purify_html' => true,
                    'required' => false,
                    'label' => 'Postal code',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']]),
                        new Length([
                            'min' => 2,
                            'max' => 15,
                            'groups' => ['attendee']])
                    ),
                ])
                ->add('street', TextType::class, [
                    'purify_html' => true,
                    'required' => false,
                    'label' => 'Street',
                    'constraints' => array(
                        new NotBlank(['groups' => ['attendee']]),
                        new Length([
                            'min' => 2,
                            'max' => 50,
                            'groups' => ['attendee']])
                    ),
                ])
                ->add('street2', TextType::class, [
