<?php

namespace App\Controller\Front;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use App\Service\AppServices;
use App\Form\CheckoutType;
use App\Entity\Order;
use App\Entity\OrderElement;
use App\Entity\Payment;
use App\Entity\CartElement;
use Symfony\Contracts\Translation\TranslatorInterface;

class GuestCheckoutController extends AbstractController {

    /**
     * @Route("/guest/cart", name="guest_cart")
     */
    public function cart(Request $request, SessionInterface $session, AppServices $services) {
        $cartItems = $session->get('guest_cart', []);

        if ($request->getMethod() == "POST") {
            $cartItems = [];
            foreach ($request->request->all() as $ticketreference => $ticketquantity) {
                if ($ticketquantity > 0) {
                    $cartItems[$ticketreference] = $ticketquantity;
                }
            }
            $session->set('guest_cart', $cartItems);
            $this->addFlash('success', 'Your cart has been updated');
        }

        // Fetch ticket details for display
        $cartItemsWithDetails = [];
        foreach ($cartItems as $ticketreference => $quantity) {
            $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();
            if ($eventTicket) {
                $cartItemsWithDetails[] = [
                    'ticket' => $eventTicket,
                    'quantity' => $quantity,
                    'reference' => $ticketreference
                ];
            }
        }

        return $this->render('Front/GuestCheckout/cart.html.twig', [
            'cartItems' => $cartItems,
            'cartItemsWithDetails' => $cartItemsWithDetails
        ]);
    }

    /**
     * @Route("/guest/cart/add", name="guest_cart_add")
     */
    public function addToCart(Request $request, SessionInterface $session, TranslatorInterface $translator) {
        $ticketreference = $request->request->get('ticketreference');
        $quantity = (int) $request->request->get('quantity', 1);

        if (!$ticketreference || $quantity <= 0) {
            $this->addFlash('error', $translator->trans('Invalid ticket selection'));
            return $this->redirectToRoute('homepage');
        }

        $cartItems = $session->get('guest_cart', []);
        $cartItems[$ticketreference] = ($cartItems[$ticketreference] ?? 0) + $quantity;
        $session->set('guest_cart', $cartItems);

        $this->addFlash('success', $translator->trans('The tickets have been successfully added to your cart'));
        return $this->redirectToRoute('guest_cart');
    }

    /**
     * @Route("/guest/cart/add-multiple", name="guest_cart_add_multiple")
     */
    public function addMultipleToCart(Request $request, SessionInterface $session, TranslatorInterface $translator, AppServices $services) {
        $tickets = $request->request->get('tickets', []);

        if (empty($tickets)) {
            $this->addFlash('error', $translator->trans('No tickets selected'));
            return $this->redirectToRoute('homepage');
        }

        $cartItems = $session->get('guest_cart', []);
        $totalAdded = 0;

        foreach ($tickets as $ticketreference => $quantity) {
            $quantity = (int) $quantity;
            if ($quantity > 0) {
                // Verify ticket exists and is on sale
                $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();
                if ($eventTicket && $eventTicket->isOnSale()) {
                    $cartItems[$ticketreference] = ($cartItems[$ticketreference] ?? 0) + $quantity;
                    $totalAdded += $quantity;
                }
            }
        }

        if ($totalAdded > 0) {
            $session->set('guest_cart', $cartItems);
            $this->addFlash('success', $translator->trans('The tickets have been successfully added to your cart'));
        } else {
            $this->addFlash('error', $translator->trans('No valid tickets were added to cart'));
        }

        return $this->redirectToRoute('guest_cart');
    }

    /**
     * @Route("/guest/checkout", name="guest_checkout")
     */
    public function checkout(Request $request, SessionInterface $session, AppServices $services, TranslatorInterface $translator) {
        $cartItems = $session->get('guest_cart', []);

        if (empty($cartItems)) {
            $this->addFlash('error', $translator->trans('Your cart is empty'));
            return $this->redirectToRoute('homepage');
        }

        $form = $this->createForm(CheckoutType::class, null, array('validation_groups' => 'guest'));
        $paymentGateways = $services->getPaymentGateways(array())->getQuery()->getResult();

        if ($request->getMethod() == "POST") {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $formData = $form->getData();

                // Create guest order
                $order = $this->createGuestOrder($cartItems, $formData, $services);

                if ($order) {
                    // Create payment
                    $payment = new Payment();
                    $payment->setOrder($order);
                    $payment->setFirstname($formData['firstname']);
                    $payment->setLastname($formData['lastname']);
                    $payment->setClientEmail($formData['email']);
                    // Store phone in street2 field temporarily until phone field is added to database
                    $payment->setStreet2('Phone: ' . ($formData['phone'] ?? ''));
                    $payment->setCountry($formData['country']);
                    $payment->setState($formData['state'] ?? '');
                    $payment->setCity($formData['city'] ?? '');
                    $payment->setPostalcode($formData['postalcode'] ?? '');
                    $payment->setStreet($formData['street'] ?? '');

                    $em = $this->getDoctrine()->getManager();
                    $em->persist($payment);
                    $em->flush();

                    // Clear cart
                    $session->remove('guest_cart');

                    // Redirect to payment
                    $selectedGateway = $request->request->get('payment_gateway');
                    if ($selectedGateway == "offline") {
                        $this->addFlash('success', $translator->trans('Your order has been successfully placed, please proceed to the payment as explained in the instructions'));
                        return $this->render('Front/GuestCheckout/success.html.twig', ['order' => $order]);
                    }

                    // Handle other payment gateways here
                    return $this->render('Front/GuestCheckout/success.html.twig', ['order' => $order]);
                }
            } else {
                $this->addFlash('error', $translator->trans('The form contains invalid data'));
            }
        }

        return $this->render('Front/GuestCheckout/checkout.html.twig', [
            'form' => $form->createView(),
            'paymentGateways' => $paymentGateways,
            'cartItems' => $cartItems
        ]);
    }

    private function createGuestOrder($cartItems, $formData, AppServices $services) {
        $em = $this->getDoctrine()->getManager();

        $order = new Order();
        $order->setReference($services->generateReference(15));
        $order->setStatus(0);
        $order->setCurrencyCcy($services->getSetting("currency_ccy"));
        $order->setCurrencySymbol($services->getSetting("currency_symbol"));
        $order->setTicketFee($services->getSetting("ticket_fee_online"));
        $order->setTicketPricePercentageCut($services->getSetting("online_ticket_price_percentage_cut"));

        foreach ($cartItems as $ticketreference => $quantity) {
            $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();

            if ($eventTicket) {
                $orderelement = new OrderElement();
                $orderelement->setOrder($order);
                $orderelement->setEventticket($eventTicket);
                $orderelement->setUnitprice($eventTicket->getSalePrice());
                $orderelement->setQuantity($quantity);
                $order->addOrderelement($orderelement);
            }
        }

        $em->persist($order);
        $em->flush();

        return $order;
    }
}
