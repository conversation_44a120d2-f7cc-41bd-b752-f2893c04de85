<?php

namespace App\Controller\Front;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use App\Service\AppServices;
use App\Form\CheckoutType;
use App\Entity\Order;
use App\Entity\OrderElement;
use App\Entity\Payment;
use App\Entity\CartElement;
use Symfony\Contracts\Translation\TranslatorInterface;
use Payum\Core\Request\GetHumanStatus;
use Payum\Core\Payum;
use Payum\Core\Security\CypherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\HttpClient\HttpClient;
use Exception;

class GuestCheckoutController extends AbstractController {

    private $payum;
    private $cypher;

    public function __construct(Payum $payum, CypherInterface $cypher) {
        $this->payum = $payum;
        $this->cypher = $cypher;
    }

    /**
     * @Route("/guest/cart", name="guest_cart")
     */
    public function cart(Request $request, SessionInterface $session, AppServices $services) {
        $cartItems = $session->get('guest_cart', []);

        if ($request->getMethod() == "POST") {
            $cartItems = [];
            foreach ($request->request->all() as $ticketreference => $ticketquantity) {
                if ($ticketquantity > 0) {
                    $cartItems[$ticketreference] = $ticketquantity;
                }
            }
            $session->set('guest_cart', $cartItems);
            $this->addFlash('success', 'Your cart has been updated');
        }

        // Fetch ticket details for display
        $cartItemsWithDetails = [];
        foreach ($cartItems as $ticketreference => $quantity) {
            $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();
            if ($eventTicket) {
                $cartItemsWithDetails[] = [
                    'ticket' => $eventTicket,
                    'quantity' => $quantity,
                    'reference' => $ticketreference
                ];
            }
        }

        return $this->render('Front/GuestCheckout/cart.html.twig', [
            'cartItems' => $cartItems,
            'cartItemsWithDetails' => $cartItemsWithDetails
        ]);
    }

    /**
     * @Route("/guest/cart/add", name="guest_cart_add")
     */
    public function addToCart(Request $request, SessionInterface $session, TranslatorInterface $translator) {
        $ticketreference = $request->request->get('ticketreference');
        $quantity = (int) $request->request->get('quantity', 1);

        if (!$ticketreference || $quantity <= 0) {
            $this->addFlash('error', $translator->trans('Invalid ticket selection'));
            return $this->redirectToRoute('homepage');
        }

        $cartItems = $session->get('guest_cart', []);
        $cartItems[$ticketreference] = ($cartItems[$ticketreference] ?? 0) + $quantity;
        $session->set('guest_cart', $cartItems);

        $this->addFlash('success', $translator->trans('The tickets have been successfully added to your cart'));
        return $this->redirectToRoute('guest_cart');
    }

    /**
     * @Route("/guest/cart/add-multiple", name="guest_cart_add_multiple")
     */
    public function addMultipleToCart(Request $request, SessionInterface $session, TranslatorInterface $translator, AppServices $services) {
        $tickets = $request->request->get('tickets', []);

        if (empty($tickets)) {
            $this->addFlash('error', $translator->trans('No tickets selected'));
            return $this->redirectToRoute('homepage');
        }

        $cartItems = $session->get('guest_cart', []);
        $totalAdded = 0;

        foreach ($tickets as $ticketreference => $quantity) {
            $quantity = (int) $quantity;
            if ($quantity > 0) {
                // Verify ticket exists and is on sale
                $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();
                if ($eventTicket && $eventTicket->isOnSale()) {
                    $cartItems[$ticketreference] = ($cartItems[$ticketreference] ?? 0) + $quantity;
                    $totalAdded += $quantity;
                }
            }
        }

        if ($totalAdded > 0) {
            $session->set('guest_cart', $cartItems);
            $this->addFlash('success', $translator->trans('The tickets have been successfully added to your cart'));
        } else {
            $this->addFlash('error', $translator->trans('No valid tickets were added to cart'));
        }

        return $this->redirectToRoute('guest_cart');
    }

    /**
     * @Route("/guest/checkout", name="guest_checkout")
     */
    public function checkout(Request $request, SessionInterface $session, AppServices $services, TranslatorInterface $translator) {
        $cartItems = $session->get('guest_cart', []);

        if (empty($cartItems)) {
            $this->addFlash('error', $translator->trans('Your cart is empty'));
            return $this->redirectToRoute('homepage');
        }

        $form = $this->createForm(CheckoutType::class, null, array('validation_groups' => 'guest'));

        // Get organizer from cart items to find the correct payment gateways
        $organizer = null;
        if (!empty($cartItems)) {
            // Get the first ticket to determine the organizer
            $firstTicketReference = array_key_first($cartItems);
            $eventTicket = $services->getEventTickets(array('reference' => $firstTicketReference))->getQuery()->getOneOrNullResult();
            if ($eventTicket && $eventTicket->getEventdate() && $eventTicket->getEventdate()->getEvent() && $eventTicket->getEventdate()->getEvent()->getOrganizer()) {
                $organizer = $eventTicket->getEventdate()->getEvent()->getOrganizer()->getSlug();
                error_log('Found organizer for guest checkout: ' . $organizer);
            }
        }

        // Debug: Let's also check what gateways exist without organizer filter
        $allGatewaysNoFilter = $services->getPaymentGateways(array())->getQuery()->getResult();
        error_log('DEBUG: All gateways without organizer filter (' . count($allGatewaysNoFilter) . '):');
        foreach ($allGatewaysNoFilter as $gw) {
            $gwOrganizer = $gw->getOrganizer() ? $gw->getOrganizer()->getSlug() : 'NULL';
            error_log('- ID: ' . $gw->getId() . ', Name: ' . $gw->getName() . ', Slug: ' . $gw->getSlug() . ', Factory: ' . $gw->getFactoryName() . ', Organizer: ' . $gwOrganizer);
        }

        // Use the same approach as the original authenticated checkout, but with organizer context
        $allPaymentGateways = $services->getPaymentGateways(array('organizer' => $organizer))->getQuery()->getResult();

        // Debug: Log what AppServices returns
        error_log('AppServices getPaymentGateways() with organizer "' . ($organizer ?? 'NULL') . '" returned ' . count($allPaymentGateways) . ' gateways:');
        foreach ($allPaymentGateways as $gw) {
            error_log('- ID: ' . $gw->getId() . ', Name: ' . $gw->getName() . ', Slug: ' . $gw->getSlug() . ', Factory: ' . $gw->getFactoryName() . ', Enabled: ' . ($gw->getEnabled() ? 'Yes' : 'No'));
        }

        // For guests, show only PesaPal gateway
        $paymentGateways = [];
        foreach ($allPaymentGateways as $gateway) {
            if ($gateway->getFactoryName() === 'pesapal' && $gateway->getEnabled()) {
                $paymentGateways[] = $gateway;
                error_log('Found PesaPal gateway for guest checkout: ' . $gateway->getSlug());
                break; // Only show the first PesaPal gateway found
            }
        }

        if (empty($paymentGateways)) {
            error_log('No PesaPal gateway found for guest checkout');
        }

        if ($request->getMethod() == "POST") {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $formData = $form->getData();

                // Create guest order
                $order = $this->createGuestOrder($cartItems, $formData, $services);

                if ($order) {
                    // Create payment
                    $payment = new Payment();
                    $payment->setOrder($order);
                    $payment->setFirstname($formData['firstname']);
                    $payment->setLastname($formData['lastname']);
                    $payment->setClientEmail($formData['email']);

                    // Only set phone if the property exists in the database
                    if (method_exists($payment, 'setPhone')) {
                        try {
                            $payment->setPhone($formData['phone'] ?? '');
                        } catch (\Exception $e) {
                            // Phone column doesn't exist in database yet, skip it
                            error_log('Could not set phone on Payment entity: ' . $e->getMessage());
                        }
                    }
                    $payment->setCountry($formData['country']);
                    $payment->setState($formData['state'] ?? '');
                    $payment->setCity($formData['city'] ?? '');
                    $payment->setPostalcode($formData['postalcode'] ?? '');
                    $payment->setStreet($formData['street'] ?? '');
                    $payment->setStreet2($formData['street2'] ?? '');

                    // Set payment details for Payum
                    $payment->setNumber(uniqid());
                    $payment->setDescription("Payment of tickets purchased on " . $services->getSetting("website_name"));
                    $payment->setCurrencyCode($services->getSetting("currency_ccy"));
                    $payment->setTotalAmount($order->getOrderElementsPriceSum(true) * 100); // Amount in cents

                    $order->setPayment($payment);

                    $em = $this->getDoctrine()->getManager();
                    $em->persist($payment);
                    $em->persist($order);
                    $em->flush();

                    // Clear cart
                    $session->remove('guest_cart');

                    // Handle payment gateway selection
                    $selectedGateway = $request->request->get('payment_gateway');

                    // Process the selected payment gateway

                    if ($selectedGateway == "offline") {
                        // For offline payments, mark as pending and show success
                        $this->addFlash('success', $translator->trans('Your order has been successfully placed, please proceed to the payment as explained in the instructions'));
                        return $this->render('Front/GuestCheckout/success.html.twig', ['order' => $order]);
                    } elseif ($selectedGateway == 'pesapal-attendee' || $selectedGateway == 'pesapal') {
                        // Handle PesaPal payment directly for guest users
                        return $this->handlePesapalPayment($order, $services, $translator, $formData);
                    } else {
                        // Handle other payment gateways using Payum - same as original flow, with organizer context
                        $paymentGateway = $services->getPaymentGateways(array('organizer' => $organizer, 'slug' => $selectedGateway))->getQuery()->getOneOrNullResult();

                        if (!$paymentGateway) {
                            $this->addFlash('error', $translator->trans('The selected payment gateway is not available'));
                            return $this->redirectToRoute('guest_checkout');
                        }

                        $order->setPaymentgateway($paymentGateway);
                        $em->persist($order);
                        $em->flush();

                        // Check if this is a custom gateway that doesn't use Payum tokens
                        $factoryName = $paymentGateway->getFactoryName();

                        if (in_array($factoryName, ['flutterwave', 'mercadopago'])) {
                            // These gateways have custom implementations, redirect accordingly
                            if ($factoryName == 'flutterwave') {
                                // Add Flutterwave redirect logic here if needed
                                $this->addFlash('error', $translator->trans('Flutterwave payment for guests is not yet implemented'));
                                return $this->redirectToRoute('guest_checkout');
                            } elseif ($factoryName == 'mercadopago') {
                                // Add MercadoPago redirect logic here if needed
                                $this->addFlash('error', $translator->trans('MercadoPago payment for guests is not yet implemented'));
                                return $this->redirectToRoute('guest_checkout');
                            }
                        } else {
                            // Use Payum for standard gateways (PayPal, Stripe, etc.)
                            try {
                                $captureToken = $this->payum->getTokenFactory()->createCaptureToken(
                                    $paymentGateway->getGatewayName(), $payment, 'guest_checkout_done'
                                );

                                return $this->redirect($captureToken->getTargetUrl());
                            } catch (Exception $e) {
                                $this->addFlash('error', $translator->trans('Payment gateway configuration error: ') . $e->getMessage());
                                return $this->redirectToRoute('guest_checkout');
                            }
                        }
                    }
                }
            } else {
                $this->addFlash('error', $translator->trans('The form contains invalid data'));
            }
        }

        return $this->render('Front/GuestCheckout/checkout.html.twig', [
            'form' => $form->createView(),
            'paymentGateways' => $paymentGateways,
            'cartItems' => $cartItems
        ]);
    }

    private function createGuestOrder($cartItems, $formData, AppServices $services) {
        $em = $this->getDoctrine()->getManager();

        $order = new Order();
        $order->setReference($services->generateReference(15));
        $order->setStatus(0);
        $order->setCurrencyCcy($services->getSetting("currency_ccy"));
        $order->setCurrencySymbol($services->getSetting("currency_symbol"));
        $order->setTicketFee($services->getSetting("ticket_fee_online"));
        $order->setTicketPricePercentageCut($services->getSetting("online_ticket_price_percentage_cut"));

        foreach ($cartItems as $ticketreference => $quantity) {
            $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();

            if ($eventTicket) {
                $orderelement = new OrderElement();
                $orderelement->setOrder($order);
                $orderelement->setEventticket($eventTicket);
                $orderelement->setUnitprice($eventTicket->getSalePrice());
                $orderelement->setQuantity($quantity);
                $order->addOrderelement($orderelement);
            }
        }

        $em->persist($order);
        $em->flush();

        return $order;
    }

    /**
     * @Route("/guest/checkout/{orderReference}/pesapal/redirect-to-payment", name="guest_checkout_pesapal_redirect_to_payment")
     */
    public function pesapalRedirectToPayment($orderReference, Request $request, TranslatorInterface $translator, AppServices $services) {
        $order = $services->getOrders(array('status' => 0, 'reference' => $orderReference))->getQuery()->getOneOrNullResult();

        if (!$order) {
            $this->addFlash('error', $translator->trans('The order can not be found'));
            return $this->redirectToRoute('homepage');
        }

        // Get organizer from the order to find the correct payment gateway
        $organizer = null;
        if ($order->getOrderElements() && count($order->getOrderElements()) > 0) {
            $firstOrderElement = $order->getOrderElements()->first();
            if ($firstOrderElement && $firstOrderElement->getEventticket() && $firstOrderElement->getEventticket()->getEventdate() && $firstOrderElement->getEventticket()->getEventdate()->getEvent() && $firstOrderElement->getEventticket()->getEventdate()->getEvent()->getOrganizer()) {
                $organizer = $firstOrderElement->getEventticket()->getEventdate()->getEvent()->getOrganizer()->getSlug();
                error_log('Found organizer for PesaPal lookup: ' . $organizer);
            }
        }

        // Use the exact same approach as the original PesaPalController, but with organizer context
        $paymentGateway = $services->getPaymentGateways(array('organizer' => $organizer, 'factoryName' => 'pesapal', 'slug' => 'pesapal-attendee'))->getQuery()->getOneOrNullResult();

        if (!$paymentGateway) {
            $this->addFlash('error', $translator->trans('The PesaPal payment gateway is not configured. Please contact the administrator.'));
            return $this->redirectToRoute('guest_checkout');
        }

        // Assign the payment gateway to the order
        $order->setPaymentgateway($paymentGateway);
        $em = $this->getDoctrine()->getManager();
        $em->persist($order);
        $em->flush();

        // Handle PesaPal payment directly for guest users
        return $this->handlePesapalPayment($order, $services, $translator, []);
    }

    private function handlePesapalPayment(Order $order, AppServices $services, TranslatorInterface $translator, array $formData = []) {
        $paymentGateway = $order->getPaymentgateway();

        if (!$paymentGateway) {
            $this->addFlash('error', $translator->trans('Payment gateway not found'));
            return $this->redirectToRoute('guest_checkout');
        }

        // Decrypt payment gateway settings using the injected cypher service
        $paymentGateway->decrypt($this->cypher);
        $paymentGatewaySettings = $paymentGateway->getSettings();

        // Determine if we're using sandbox or live environment
        $isSandbox = isset($paymentGatewaySettings['sandbox']) && $paymentGatewaySettings['sandbox'];
        $baseUrl = $isSandbox ? 'https://cybqa.pesapal.com/pesapalv3' : 'https://pay.pesapal.com/v3';

        // Get payment information from the order
        $payment = $order->getPayment();
        if (!$payment) {
            $this->addFlash('error', $translator->trans('Payment information not found'));
            return $this->redirectToRoute('guest_checkout');
        }

        $firstName = $payment->getFirstname();
        $lastName = $payment->getLastname();
        $email = $payment->getClientEmail();

        // Get phone number from form data or payment entity
        $phone = '';
        if (!empty($formData['phone'])) {
            $phone = $formData['phone'];
        } else {
            // Try to get from payment entity as fallback
            try {
                $phone = $payment->getPhone() ?? '';
            } catch (\Exception $e) {
                // Phone property might not exist in database yet
                error_log('Phone property not available in Payment entity: ' . $e->getMessage());
                $phone = '';
            }
        }

        // Generate callback URLs for guest checkout
        $callbackUrl = $this->generateUrl('guest_pesapal_payment_callback', [
            'orderReference' => $order->getReference()
        ], UrlGeneratorInterface::ABSOLUTE_URL);

        try {
            // Create the HTTP client
            $httpClient = HttpClient::create();

            // Get auth token
            $authToken = $this->getAuthToken($paymentGatewaySettings, $baseUrl, $httpClient);

            // Submit Order to PesaPal
            $orderData = [
                'id' => $order->getReference(),
                'currency' => $order->getCurrencyCcy(),
                'amount' => $order->getOrderElementsPriceSum(true),
                'description' => 'Payment for tickets - Order #' . $order->getReference(),
                'callback_url' => $callbackUrl,
                'notification_id' => $order->getReference(),
                'billing_address' => [
                    'email_address' => $email,
                    'phone_number' => $phone,
                    'country_code' => 'KE',
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                ]
            ];

            $response = $httpClient->request('POST', $baseUrl . '/api/Transactions/SubmitOrderRequest', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $authToken,
                    'Content-Type' => 'application/json'
                ],
                'json' => $orderData
            ]);

            $responseData = $response->toArray();

            if ($response->getStatusCode() !== 200 || !isset($responseData['redirect_url'])) {
                throw new \Exception('Invalid response from PesaPal: ' . json_encode($responseData));
            }

            // Create the iframe URL
            $iframeUrl = $responseData['redirect_url'];

            return $this->render('Front/GuestCheckout/pesapal-iframe.html.twig', [
                'iframeUrl' => $iframeUrl,
                'order' => $order
            ]);

        } catch (\Exception $e) {
            $this->addFlash('error', $translator->trans('Error connecting to PesaPal: ') . $e->getMessage());
            return $this->redirectToRoute('guest_checkout');
        }
    }

    private function getAuthToken($paymentGatewaySettings, $baseUrl, $httpClient) {
        $authData = [
            'consumer_key' => $paymentGatewaySettings['pesapal_consumer_key'],
            'consumer_secret' => $paymentGatewaySettings['pesapal_consumer_secret']
        ];

        $response = $httpClient->request('POST', $baseUrl . '/api/Auth/RequestToken', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => $authData
        ]);

        $authResponse = $response->toArray();

        if (!isset($authResponse['token'])) {
            throw new \Exception('Failed to get auth token from PesaPal');
        }

        return $authResponse['token'];
    }

    /**
     * @Route("/guest/checkout/done", name="guest_checkout_done")
     */
    public function checkoutDone(Request $request, TranslatorInterface $translator, AppServices $services) {
        try {
            $token = $this->payum->getHttpRequestVerifier()->verify($request);
            $gateway = $this->payum->getGateway($token->getGatewayName());
        } catch (Exception $e) {
            $this->addFlash('error', $translator->trans('An error has occured while processing your request'));
            return $this->redirectToRoute('homepage');
        }

        $gateway->execute($status = new GetHumanStatus($token));
        $payment = $status->getFirstModel();
        $this->payum->getHttpRequestVerifier()->invalidate($token);

        $order = $payment->getOrder();

        if ($status->isCaptured() || $status->isAuthorized()) {
            // Payment successful
            $order->setStatus(1);
            $em = $this->getDoctrine()->getManager();
            $em->persist($order);
            $em->flush();

            $this->addFlash('success', $translator->trans('Payment completed successfully'));
            return $this->render('Front/GuestCheckout/success.html.twig', ['order' => $order]);
        } else {
            // Payment failed
            $this->addFlash('error', $translator->trans('Payment failed. Please try again.'));
            return $this->redirectToRoute('guest_checkout');
        }
    }
}
