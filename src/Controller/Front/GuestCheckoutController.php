<?php

namespace App\Controller\Front;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use App\Service\AppServices;
use App\Form\CheckoutType;
use App\Entity\Order;
use App\Entity\OrderElement;
use App\Entity\Payment;
use App\Entity\CartElement;
use Symfony\Contracts\Translation\TranslatorInterface;
use Payum\Core\Request\GetHumanStatus;
use Payum\Core\Payum;
use Exception;

class GuestCheckoutController extends AbstractController {

    private $payum;

    public function __construct(Payum $payum) {
        $this->payum = $payum;
    }

    /**
     * @Route("/guest/cart", name="guest_cart")
     */
    public function cart(Request $request, SessionInterface $session, AppServices $services) {
        $cartItems = $session->get('guest_cart', []);

        if ($request->getMethod() == "POST") {
            $cartItems = [];
            foreach ($request->request->all() as $ticketreference => $ticketquantity) {
                if ($ticketquantity > 0) {
                    $cartItems[$ticketreference] = $ticketquantity;
                }
            }
            $session->set('guest_cart', $cartItems);
            $this->addFlash('success', 'Your cart has been updated');
        }

        // Fetch ticket details for display
        $cartItemsWithDetails = [];
        foreach ($cartItems as $ticketreference => $quantity) {
            $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();
            if ($eventTicket) {
                $cartItemsWithDetails[] = [
                    'ticket' => $eventTicket,
                    'quantity' => $quantity,
                    'reference' => $ticketreference
                ];
            }
        }

        return $this->render('Front/GuestCheckout/cart.html.twig', [
            'cartItems' => $cartItems,
            'cartItemsWithDetails' => $cartItemsWithDetails
        ]);
    }

    /**
     * @Route("/guest/cart/add", name="guest_cart_add")
     */
    public function addToCart(Request $request, SessionInterface $session, TranslatorInterface $translator) {
        $ticketreference = $request->request->get('ticketreference');
        $quantity = (int) $request->request->get('quantity', 1);

        if (!$ticketreference || $quantity <= 0) {
            $this->addFlash('error', $translator->trans('Invalid ticket selection'));
            return $this->redirectToRoute('homepage');
        }

        $cartItems = $session->get('guest_cart', []);
        $cartItems[$ticketreference] = ($cartItems[$ticketreference] ?? 0) + $quantity;
        $session->set('guest_cart', $cartItems);

        $this->addFlash('success', $translator->trans('The tickets have been successfully added to your cart'));
        return $this->redirectToRoute('guest_cart');
    }

    /**
     * @Route("/guest/cart/add-multiple", name="guest_cart_add_multiple")
     */
    public function addMultipleToCart(Request $request, SessionInterface $session, TranslatorInterface $translator, AppServices $services) {
        $tickets = $request->request->get('tickets', []);

        if (empty($tickets)) {
            $this->addFlash('error', $translator->trans('No tickets selected'));
            return $this->redirectToRoute('homepage');
        }

        $cartItems = $session->get('guest_cart', []);
        $totalAdded = 0;

        foreach ($tickets as $ticketreference => $quantity) {
            $quantity = (int) $quantity;
            if ($quantity > 0) {
                // Verify ticket exists and is on sale
                $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();
                if ($eventTicket && $eventTicket->isOnSale()) {
                    $cartItems[$ticketreference] = ($cartItems[$ticketreference] ?? 0) + $quantity;
                    $totalAdded += $quantity;
                }
            }
        }

        if ($totalAdded > 0) {
            $session->set('guest_cart', $cartItems);
            $this->addFlash('success', $translator->trans('The tickets have been successfully added to your cart'));
        } else {
            $this->addFlash('error', $translator->trans('No valid tickets were added to cart'));
        }

        return $this->redirectToRoute('guest_cart');
    }

    /**
     * @Route("/guest/checkout", name="guest_checkout")
     */
    public function checkout(Request $request, SessionInterface $session, AppServices $services, TranslatorInterface $translator) {
        $cartItems = $session->get('guest_cart', []);

        if (empty($cartItems)) {
            $this->addFlash('error', $translator->trans('Your cart is empty'));
            return $this->redirectToRoute('homepage');
        }

        $form = $this->createForm(CheckoutType::class, null, array('validation_groups' => 'guest'));
        $paymentGateways = $services->getPaymentGateways(array())->getQuery()->getResult();

        if ($request->getMethod() == "POST") {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $formData = $form->getData();

                // Create guest order
                $order = $this->createGuestOrder($cartItems, $formData, $services);

                if ($order) {
                    // Create payment
                    $payment = new Payment();
                    $payment->setOrder($order);
                    $payment->setFirstname($formData['firstname']);
                    $payment->setLastname($formData['lastname']);
                    $payment->setClientEmail($formData['email']);
                    $payment->setPhone($formData['phone'] ?? '');
                    $payment->setCountry($formData['country']);
                    $payment->setState($formData['state'] ?? '');
                    $payment->setCity($formData['city'] ?? '');
                    $payment->setPostalcode($formData['postalcode'] ?? '');
                    $payment->setStreet($formData['street'] ?? '');
                    $payment->setStreet2($formData['street2'] ?? '');

                    // Set payment details for Payum
                    $payment->setNumber(uniqid());
                    $payment->setDescription("Payment of tickets purchased on " . $services->getSetting("website_name"));
                    $payment->setCurrencyCode($services->getSetting("currency_ccy"));
                    $payment->setTotalAmount($order->getOrderElementsPriceSum(true) * 100); // Amount in cents

                    $order->setPayment($payment);

                    $em = $this->getDoctrine()->getManager();
                    $em->persist($payment);
                    $em->persist($order);
                    $em->flush();

                    // Clear cart
                    $session->remove('guest_cart');

                    // Handle payment gateway selection
                    $selectedGateway = $request->request->get('payment_gateway');

                    if ($selectedGateway == "offline") {
                        // For offline payments, mark as pending and show success
                        $this->addFlash('success', $translator->trans('Your order has been successfully placed, please proceed to the payment as explained in the instructions'));
                        return $this->render('Front/GuestCheckout/success.html.twig', ['order' => $order]);
                    } elseif ($selectedGateway == 'pesapal') {
                        // Redirect to Pesapal payment processing
                        return $this->redirectToRoute('guest_checkout_pesapal_redirect_to_payment', ['orderReference' => $order->getReference()]);
                    } else {
                        // Handle other payment gateways using Payum
                        $paymentGateway = $services->getPaymentGateways(array('slug' => $selectedGateway))->getQuery()->getOneOrNullResult();

                        if (!$paymentGateway) {
                            $this->addFlash('error', $translator->trans('The selected payment gateway is not available'));
                            return $this->redirectToRoute('guest_checkout');
                        }

                        $order->setPaymentgateway($paymentGateway);
                        $em->persist($order);
                        $em->flush();

                        // Create capture token for Payum
                        $captureToken = $this->payum->getTokenFactory()->createCaptureToken(
                            $paymentGateway->getGatewayName(), $payment, 'guest_checkout_done'
                        );

                        return $this->redirect($captureToken->getTargetUrl());
                    }
                }
            } else {
                $this->addFlash('error', $translator->trans('The form contains invalid data'));
            }
        }

        return $this->render('Front/GuestCheckout/checkout.html.twig', [
            'form' => $form->createView(),
            'paymentGateways' => $paymentGateways,
            'cartItems' => $cartItems
        ]);
    }

    private function createGuestOrder($cartItems, $formData, AppServices $services) {
        $em = $this->getDoctrine()->getManager();

        $order = new Order();
        $order->setReference($services->generateReference(15));
        $order->setStatus(0);
        $order->setCurrencyCcy($services->getSetting("currency_ccy"));
        $order->setCurrencySymbol($services->getSetting("currency_symbol"));
        $order->setTicketFee($services->getSetting("ticket_fee_online"));
        $order->setTicketPricePercentageCut($services->getSetting("online_ticket_price_percentage_cut"));

        foreach ($cartItems as $ticketreference => $quantity) {
            $eventTicket = $services->getEventTickets(array('reference' => $ticketreference))->getQuery()->getOneOrNullResult();

            if ($eventTicket) {
                $orderelement = new OrderElement();
                $orderelement->setOrder($order);
                $orderelement->setEventticket($eventTicket);
                $orderelement->setUnitprice($eventTicket->getSalePrice());
                $orderelement->setQuantity($quantity);
                $order->addOrderelement($orderelement);
            }
        }

        $em->persist($order);
        $em->flush();

        return $order;
    }

    /**
     * @Route("/guest/checkout/{orderReference}/pesapal/redirect-to-payment", name="guest_checkout_pesapal_redirect_to_payment")
     */
    public function pesapalRedirectToPayment($orderReference, Request $request, TranslatorInterface $translator, AppServices $services) {
        $order = $services->getOrders(array('status' => 0, 'reference' => $orderReference))->getQuery()->getOneOrNullResult();

        if (!$order) {
            $this->addFlash('error', $translator->trans('The order can not be found'));
            return $this->redirectToRoute('homepage');
        }

        // Get the PesaPal payment gateway
        $paymentGateway = $services->getPaymentGateways(array('factoryName' => 'pesapal', 'slug' => 'pesapal-attendee'))->getQuery()->getOneOrNullResult();

        if (!$paymentGateway) {
            $this->addFlash('error', $translator->trans('The PesaPal payment gateway is not configured'));
            return $this->redirectToRoute('guest_checkout');
        }

        // Assign the payment gateway to the order
        $order->setPaymentgateway($paymentGateway);
        $em = $this->getDoctrine()->getManager();
        $em->persist($order);
        $em->flush();

        // Redirect to the main PesaPal controller
        return $this->redirectToRoute('dashboard_attendee_checkout_pesapal_redirect_to_payment', [
            'orderReference' => $orderReference
        ]);
    }

    /**
     * @Route("/guest/checkout/done", name="guest_checkout_done")
     */
    public function checkoutDone(Request $request, TranslatorInterface $translator, AppServices $services) {
        try {
            $token = $this->payum->getHttpRequestVerifier()->verify($request);
            $gateway = $this->payum->getGateway($token->getGatewayName());
        } catch (Exception $e) {
            $this->addFlash('error', $translator->trans('An error has occured while processing your request'));
            return $this->redirectToRoute('homepage');
        }

        $gateway->execute($status = new GetHumanStatus($token));
        $payment = $status->getFirstModel();
        $this->payum->getHttpRequestVerifier()->invalidate($token);

        $order = $payment->getOrder();

        if ($status->isCaptured() || $status->isAuthorized()) {
            // Payment successful
            $order->setStatus(1);
            $em = $this->getDoctrine()->getManager();
            $em->persist($order);
            $em->flush();

            $this->addFlash('success', $translator->trans('Payment completed successfully'));
            return $this->render('Front/GuestCheckout/success.html.twig', ['order' => $order]);
        } else {
            // Payment failed
            $this->addFlash('error', $translator->trans('Payment failed. Please try again.'));
            return $this->redirectToRoute('guest_checkout');
        }
    }
}
