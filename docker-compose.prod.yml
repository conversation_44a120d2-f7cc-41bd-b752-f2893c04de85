version: '3'

services:
  mysql:
    image: mysql:5.7
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_SQL_MODE: ''
    volumes:
      - mysql_data:/var/lib/mysql
    command: --sql-mode=""
    networks:
      - patatickets_network

  php:
    build:
      context: .
      dockerfile: docker/Dockerfile.prod
    restart: always
    volumes:
      - ./:/var/www/html
    env_file:
      - .env
    depends_on:
      - mysql
    networks:
      - patatickets_network

  nginx:
    image: nginx:1.21
    restart: always
    ports:
      - "8080:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    networks:
      - patatickets_network

networks:
  patatickets_network:

volumes:
  mysql_data:
