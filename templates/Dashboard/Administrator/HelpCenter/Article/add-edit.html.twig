{% extends "Global/layout.html.twig" %}

{% set pagetitle = 'Add a new help center article' | trans %}

{% if article.id %}{% set pagetitle = 'Update the help center article' | trans %}{% endif %}

{% block title %}{{pagetitle}}{% endblock %}

{% block content %}

    {% set navigation = [{ "dashboard_index": ('Dashboard' | trans),  "dashboard_administrator_help_center_article": ('Manage help center articles' | trans), "current":(pagetitle) }] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <aside class="col-lg-3 pt-3 pt-lg-0">
                    {% include "Dashboard/sidebar.html.twig" %}
                </aside>
                <div class="col-lg-9 mt-4 mt-lg-0">
                    <div class="card box">
                        <div class="card-body">
                            {{ form_start(form, {'attr': {'novalidate': 'novalidate'}}) }}
                            {{ form_widget(form) }}
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

{% endblock %}