{% block body_html %}
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
        <head>
            <meta name="viewport" content="width=device-width" />
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
            <title>{{ "Your tickets bought from"|trans }} {{ services.getSetting("website_name") }}</title>

            {% if app.request.locale == "ar" %}

                <style>

                    .body-wrap * {
                        direction: rtl !important;
                        text-align: right !important;
                        font-family: 'Cairo', sans-serif !important;
                    }

                </style>

            {% endif %}

            <style type="text/css">
                img {
                    max-width: 100%;
                }
                body {
                    -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: none; width: 100% !important; height: 100%; line-height: 1.6em;
                }
                body {
                    background-color: #f6f6f6 !important;
                }
                /* Prevent dark mode from affecting email */
                [data-ogsc] body {
                    background-color: #f6f6f6 !important;
                }
                @media (prefers-color-scheme: dark) {
                    body {
                        background-color: #f6f6f6 !important;
                    }
                    .main {
                        background-color: #fff !important;
                    }
                    .body-wrap {
                        background-color: #f6f6f6 !important;
                    }
                }
                @media only screen and (max-width: 640px) {
                    body {
                        padding: 0 !important;
                    }
                    h1 {
                        font-weight: 800 !important; margin: 20px 0 5px !important;
                    }
                    h2 {
                        font-weight: 800 !important; margin: 20px 0 5px !important;
                    }
                    h3 {
                        font-weight: 800 !important; margin: 20px 0 5px !important;
                    }
                    h4 {
                        font-weight: 800 !important; margin: 20px 0 5px !important;
                    }
                    h1 {
                        font-size: 22px !important;
                    }
                    h2 {
                        font-size: 18px !important;
                    }
                    h3 {
                        font-size: 16px !important;
                    }
                    .container {
                        padding: 0 !important; width: 100% !important;
                    }
                    .content {
                        padding: 0 !important;
                    }
                    .content-wrap {
                        padding: 10px !important;
                    }
                    .invoice {
                        width: 100% !important;
                    }
                    td p {
                        margin-top: 0;
                    }
                }
            </style>
        </head>

        <body itemscope itemtype="http://schema.org/EmailMessage" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: none; width: 100% !important; height: 100%; line-height: 1.6em; background-color: #f6f6f6; margin: 0;" bgcolor="#f6f6f6">

            <table class="body-wrap" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; width: 100%; background-color: #f6f6f6; margin: 0;" bgcolor="#f6f6f6"><tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;"><td style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0;" valign="top"></td>
                    <td class="container" width="600" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; display: block !important; max-width: 600px !important; clear: both !important; margin: 0 auto;" valign="top">
                        <div class="content" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; max-width: 600px; display: block; margin: 0 auto; padding: 20px;">
                            <table class="main" width="100%" cellpadding="0" cellspacing="0" itemprop="action" itemscope itemtype="http://schema.org/ConfirmAction" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; border-radius: 3px; background-color: #fff; margin: 0; border: 1px solid #e9e9e9;" bgcolor="#fff">
                                <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;"><td class="content-wrap" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 20px;" valign="top">
                                        <meta itemprop="name" content="Quote Request" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;" /><table width="100%" cellpadding="0" cellspacing="0" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="vertical-align: top; margin: 0; padding: 0 0 20px; text-align: center;" valign="top">
                                                    <img src="{{ absolute_url(asset(services.getAppLayoutSettings.getLogoPath)) }}" />
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    <h2>{{ "Order confirmation"|trans }}</h2>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    {{ "Hey"|trans }} {{ order.user ? order.user.getCrossRoleName : (order.payment.firstname ~ ' ' ~ order.payment.lastname) }}, {{ "your tickets are ready to go!"|trans }}
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px; text-align: center;" valign="top">
                                                    <img src="{{ absolute_url(asset('assets/img/illustrations/order-confirmation.png')) }}" />
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    {{ "This email is sent as a confirmation of your order"|trans }} <a href="{{ absolute_url(path('dashboard_attendee_order_details', { reference: order.reference })) }}" target="_blank">#{{ order.reference }}</a> {{ "placed on"|trans }} {{ order.payment.details["TIMESTAMP"] is defined ? order.payment.details["TIMESTAMP"]|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) : order.updatedAt|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    <h4>{{ "Payment"|trans }}</h4>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    {% if order.payment.details["DESC"] is defined %}
                                                        <p>{{ "Description"|trans }}: {{ order.payment.details["DESC"] }}</p>
                                                    {% endif %}
                                                    {% if order.payment.details["AMT"] is defined %}
                                                        <p>{{ "Order total"|trans }}: {{ order.payment.details["AMT"] }}</p>
                                                    {% endif %}
                                                    {% if order.payment.details["CURRENCYCODE"] is defined %}
                                                        <p>{{ "Currency"|trans }}: {{ order.payment.details["CURRENCYCODE"] }}</p>
                                                    {% endif %}
                                                    <p>{{ "Payment method"|trans }}: {{ order.paymentgateway.name }}</p>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    <h4>{{ "Billing information"|trans }}</h4>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    <p>{{ "Full name"|trans }}: {{ order.payment.firstname ~ " " ~ order.payment.lastname }}</p>
                                                    <p>{{ "Email"|trans }}: {{ order.payment.clientEmail }}</p>
                                                    <p>{{ "Address"|trans }}: {{ order.payment.stringifyAddress }}</p>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    <h4>{{ "Tickets"|trans }}</h4>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    <table width="100%">
                                                        {% for orderElement in order.orderelements %}
                                                            <tr width="100%" valign="top">
                                                                <td width="70%">
                                                                    <p style="margin-top: 0;">{{ orderElement.eventticket.eventdate.event.name }}</p>
                                                                    <p>{{ "Date"|trans }}: {{ orderElement.eventticket.eventdate.startdate|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}</p>
                                                                    {% if orderElement.eventticket.eventdate.venue %}
                                                                        <p>{{ "Venue"|trans }}: {{ orderElement.eventticket.eventdate.venue.name }}: {{ orderElement.eventticket.eventdate.venue.stringifyAddress }}</p>
                                                                    {% else %}
                                                                        <p>{{ "Where"|trans ~ ": " ~ "Online"|trans }}</p>
                                                                    {% endif %}
                                                                </td>
                                                                <td width="15%">x {{ orderElement.quantity }}</td>
                                                                <td width="15%">{{ orderElement.eventticket.free ? "Free"|trans : ((services.getSetting('currency_position') == 'left' ? services.getSetting('currency_symbol') : '') ~ orderElement.getPrice() ~ (services.getSetting('currency_position') == 'right' ? services.getSetting('currency_symbol') : '')) }}</td>
                                                            </tr>
                                                            {% if not loop.last %}
                                                                <tr width="100%" valign="top">
                                                                    <td colspan="3" width="100%">
                                                                        <hr style="border-top: 1px solid rgba(0, 0, 0, 0.1);" />
                                                                    </td>
                                                                </tr>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                                <td class="content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                                                    &mdash; {{ 'Best regards, the %website_name% team'|trans({'%website_name%': services.getSetting("website_name")}) }}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            <div class="footer" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; width: 100%; clear: both; color: #999; margin: 0; padding: 20px;">
                                <table width="100%" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                    <tr style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
                                        <td class="aligncenter content-block" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 12px; vertical-align: top; color: #999; text-align: center; margin: 0; padding: 0 0 20px;" align="center" valign="top"><a href="{{ services.getSetting("website_url") }}" style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 12px; color: #999; text-decoration: underline; margin: 0;">{{ services.getSetting("website_name") }}</a> © {{ "now"|date('Y') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </td>
                    <td style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; vertical-align: top; margin: 0;" valign="top"></td>
                </tr>
            </table>
        </body>
    </html>
{% endblock %}