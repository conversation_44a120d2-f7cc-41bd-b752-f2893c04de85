{% extends "Global/layout.html.twig" %}

{% set pagetitle = 'Payout requests' | trans %}

{% block title %}{{pagetitle}}{% endblock %}

{% block content %}

    {% set navigation = [{"current":(pagetitle)}] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <aside class="col-lg-3 pt-3 pt-lg-0">
                    {% include "Dashboard/sidebar.html.twig" %}
                </aside>
                <div class="col-lg-9 mt-4 mt-lg-0">
                    {% if is_granted("ROLE_ORGANIZER") %}
                        {% include "Global/message.html.twig" with { type: "info", message: ('To submit a new payout request for an event date, go to the My events page and click on the Request payout link next to the event'|trans), icon: "fas fa-info-circle" } %}
                    {% endif %}
                    <div class="box shadow-none bg-gray mb-4">
                        <div class="row">
                            <div class="col-sm-12 col-lg-3 text-center text-lg-left mb-3 mb-lg-0">
                                <span class="center-lg-y text-muted">{{ "%resultsCount% result(s) found"|trans({'%resultsCount%': payoutRequests.getTotalItemCount}) }}</span>
                            </div>
                            <div class="col-sm-12 col-lg-5 text-center text-lg-right">
                                <label for="sortable-select" class="display-inline">{{ "Sort by"|trans }}
                                    <select id="sortable-select" class="form-control display-inline-block bg-white select2 ml-3" data-placeholder="{{ "Select an option"|trans }}" style="width: 50%;">
                                        {{ knp_pagination_sortable(payoutRequests, "Creation date"|trans ~ ' (' ~ "desc"|trans ~ ')', "p.createdAt", {"direction": "desc", "criteria": "p.createdAt"}) }}
                                        {{ knp_pagination_sortable(payoutRequests, "Creation date"|trans ~ ' (' ~ "asc"|trans ~ ')', "p.createdAt", {"direction": "asc", "criteria": "p.createdAt"}) }}
                                        {{ knp_pagination_sortable(payoutRequests, "Processing date"|trans ~ ' (' ~ "desc"|trans ~ ')', "p.updatedAt", {"direction": "desc", "criteria": "p.updatedAt"}) }}
                                        {{ knp_pagination_sortable(payoutRequests, "Processing date"|trans ~ ' (' ~ "asc"|trans ~ ')', "p.updatedAt", {"direction": "asc", "criteria": "p.updatedAt"}) }}
                                    </select>
                                </label>
                            </div>
                            <div class="col-sm-12 col-lg-4 text-center text-lg-right">
                                <a href="#search-filters" class="btn btn-primary has-tooltip" data-toggle="collapse" title="{{ "Toggle display the search filters"|trans }}" aria-expanded="false" aria-controls="search-filters"><i class="fab fa-searchengin fa-lg"></i></a>
                            </div>
                            <div id="search-filters" class="col-sm-12 col-lg-12 text-center mb-3 mb-lg-0 collapse show">
                                <hr>
                                <form>
                                    <div class="row">
                                        <div class="col-12 col-sm-4 text-left mb-4">
                                            <label for="reference">{{ "Reference"|trans }}</label>
                                            <input type="text" class="mr-sm-2 mb-2 mb-sm-0 form-control bg-white" id="reference" name="reference">
                                        </div>
                                        <div class="col-12 col-sm-4 text-left mb-4">
                                            <label for="eventdate">{{ "Event date"|trans }} <i class="fas fa-info-circle" data-toggle="tooltip" title="{{ "Select a specific event date"|trans }}"></i></label>
                                            <input type="text" class="mr-sm-2 mb-2 mb-sm-0 autocomplete bg-white" id="eventdate" name="eventdate" data-url-list="{{ path('get_eventdates_by_event', { eventSlug : "all" }) }}" data-minimum-input-length="0">
                                        </div>
                                        {% if is_granted("ROLE_ADMINISTRATOR") %}
                                            <div class="col-12 col-sm-4 text-left mb-4">
                                                <label for="organizer">{{ "Organizer"|trans }} <i class="fas fa-info-circle" data-toggle="tooltip" title="{{ "All payout requests for an organizer"|trans }}"></i></label>
                                                <input type="text" class="mr-sm-2 mb-2 mb-sm-0 autocomplete bg-white" id="organizer" name="organizer" data-url-list="{{ path('get_organizers') }}">
                                            </div>
                                        {% endif %}
                                        <div class="col-12 col-sm-4 text-left mb-4">
                                            <label for="datefrom">{{ "From date"|trans }}</label>
                                            <input type="text" class="mr-sm-2 mb-2 mb-sm-0 form-control bg-white datetimepicker" id="datefrom" name="datefrom">
                                        </div>
                                        <div class="col-12 col-sm-4 text-left mb-4">
                                            <label for="dateto">{{ "Until date"|trans }}</label>
                                            <input type="text" class="mr-sm-2 mb-2 mb-sm-0 form-control bg-white datetimepicker" id="dateto" name="dateto">
                                        </div>
                                        <div class="col-12 col-sm-4 text-left mb-4">
                                            <div class="custom-control custom-checkbox">
                                                <input id="status-all" type="radio" name="status" value="all" class="custom-control-input">
                                                <label for="status-all" class="custom-control-label">{{ "All"|trans }}</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input id="status-paid" type="radio" name="status" value="1" class="custom-control-input">
                                                <label for="status-paid" class="custom-control-label">{{ "Approved"|trans }}</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input id="status-waiting-for-payment" type="radio" name="status" value="0" class="custom-control-input">
                                                <label for="status-waiting-for-payment" class="custom-control-label">{{ "Pending"|trans }}</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input id="status-canceled" type="radio" name="status" value="-1" class="custom-control-input">
                                                <label for="status-canceled" class="custom-control-label">{{ "Canceled"|trans }}</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input id="status-failed" type="radio" name="status" value="-2" class="custom-control-input">
                                                <label for="status-failed" class="custom-control-label">{{ "Failed"|trans }}</label>
                                            </div>
                                        </div>
                                        <div class="col-12 col-sm-4 text-left mb-4">
                                            <button type="submit" class="btn btn-primary" data-toggle="tooltip" title="{{ "Search"|trans }}"><i class="fas fa-search"></i></button>
                                            <button type="reset" class="btn btn-primary" data-toggle="tooltip" title="{{ "Reset"|trans }}"><i class="fas fa-search-minus"></i></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">

                            <div class="card">

                                {% if payoutRequests.getTotalItemCount %}

                                    <div class="table-responsive">
                                        <table class="table table-hover table-vcenter">
                                            <thead>
                                                <tr>
                                                    <th class="text-center">{{ "Reference"|trans }}</th>
                                                    <th>{{'Event date'|trans}}</th>
                                                        {% if is_granted("ROLE_ADMINISTRATOR") %}
                                                        <th>{{'Organizer'|trans}}</th>
                                                        {% endif %}
                                                    <th>{{'Payout method'|trans}}</th>
                                                    <th>{{'Net sales'|trans}}</th>
                                                    <th>{{'Request date'|trans}}</th>
                                                    <th>{{'Status'|trans}}</th>
                                                    <th class="text-center"><i class="fas fa-cog"></i></th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                {% for payoutRequest in payoutRequests %}

                                                    <tr>
                                                        <td class="text-center text-muted text-sm">
                                                            {{ payoutRequest.reference }}
                                                        </td>
                                                        <td class="text-sm">
                                                            {{ payoutRequest.eventDate.event.name }}
                                                            <br>
                                                            {{ payoutRequest.eventDate.startdate|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}
                                                        </td>
                                                        {% if is_granted("ROLE_ADMINISTRATOR") %}
                                                            <td class="text-sm">
                                                                {{ payoutRequest.organizer.name }}
                                                            </td>
                                                        {% endif %}
                                                        <td class="text-sm">
                                                            <img src="{{  asset('assets/img/icons/payment/'~ payoutRequest.paymentGateway.name|lower ~'.svg') }}" class="img-80-80" />
                                                        </td>
                                                        <td class="text-sm">
                                                            <b>{{ services.getSetting('currency_position') == 'left' ? services.getSetting('currency_symbol') : '' }}{{ payoutRequest.eventDate.getOrganizerPayoutAmount() }}{{ services.getSetting('currency_position') == 'right' ? services.getSetting('currency_symbol') : '' }}</b>
                                                        </td>
                                                        <td class="text-sm">
                                                            {{ payoutRequest.createdAt|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}
                                                        </td>
                                                        <td class="text-sm">
                                                            <span class="badge badge-{{ payoutRequest.getStatusClass }}">{{ payoutRequest.stringifyStatus|trans }}</span>
                                                            {% if payoutRequest.status != 0 %}
                                                                <i class="far fa-clock text-primary ml-1" data-toggle="tooltip" title="{{ "Processed"|trans ~ ": " ~ payoutRequest.updatedAt|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}"></i>
                                                            {% endif %}
                                                            {% if payoutRequest.note %}
                                                                <i class="far fa-comment-dots text-primary ml-1" data-toggle="tooltip" title="{{ "Note"|trans ~ ": " ~ payoutRequest.note }}"></i>
                                                            {% endif %}
                                                            {% if payoutRequest.deletedAt %}
                                                                <br>
                                                                <br>
                                                                <span class="badge badge-danger"><i class="fas fa-times fa-fw"></i> {{ "Deleted"|trans }}</span>
                                                            {% endif %}

                                                        </td>
                                                        <td class="text-center">
                                                            <div class="item-action dropdown">
                                                                <a href="javascript:void(0)" data-toggle="dropdown" data-boundary="window" class="icon"><i class="fas fa-ellipsis-v"></i></a>
                                                                <div class="dropdown-menu dropdown-menu-right">
                                                                    {% if is_granted('ROLE_ORGANIZER') %}
                                                                        <a href="{{ path('dashboard_organizer_payout_request_details', { reference : payoutRequest.reference }) }}" class="dropdown-item"><i class="dropdown-icon fas fa-file-invoice fa-fw text-muted"></i> {{ "Details"|trans }} </a>
                                                                        {% if payoutRequest.status == 0 %}
                                                                            <span data-target="{{ path('dashboard_organizer_payout_request_cancel', { reference : payoutRequest.reference }) }}" class="dropdown-item cursor-pointer payoutRequest-cancel-button" data-confirmation-text="{{ "You are about to cancel this payout request (this action cannot be undone), the event date related to this payout request will be unlocked"|trans }}"><i class="dropdown-icon fas fa-window-close fa-fw text-muted"></i> {{ "Cancel"|trans }}</span>
                                                                        {% endif %}
                                                                    {% elseif is_granted('ROLE_ADMINISTRATOR') %}
                                                                        <a href="{{ path('dashboard_administrator_payout_request_details', { reference : payoutRequest.reference }) }}" class="dropdown-item"><i class="dropdown-icon fas fa-file-invoice fa-fw text-muted"></i> {{ "Details"|trans }} </a>
                                                                        {% if payoutRequest.status == 1 or payoutRequest.status == -2 %}
                                                                            <span class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#payoutRequest-{{ payoutRequest.reference }}-payment-details"><i class="dropdown-icon fas fa-file-alt fa-fw text-muted"></i> {{ "Payment details"|trans }}</span>
                                                                        {% elseif payoutRequest.status == 0 %}
                                                                            <span data-target="{{ path('dashboard_administrator_payout_request_approve', { reference : payoutRequest.reference }) }}" class="dropdown-item requires-confirmation" data-confirmation-text="{{ "You are about to approve the payout request"|trans }}<br>{{ "Amount" }}: <b>{{ services.getSetting('currency_position') == 'left' ? services.getSetting('currency_symbol') : '' }}{{ payoutRequest.eventDate.getOrganizerPayoutAmount }}{{ services.getSetting('currency_position') == 'right' ? services.getSetting('currency_symbol') : '' }}</b><br>{{ 'Payout method'|trans }}: <b>{{ payoutRequest.paymentGateway.name }}</b>"><i class="dropdown-icon fas fa-money-check-alt fa-fw text-muted"></i> {{ "Approve"|trans }} </span>
                                                                            <span data-target="{{ path('dashboard_administrator_payout_request_cancel', { reference : payoutRequest.reference }) }}" class="dropdown-item cursor-pointer payoutRequest-cancel-button" data-confirmation-text="{{ "You are about to cancel this payout request (this action cannot be undone), the event date related to this payout request will be unlocked"|trans }}"><i class="dropdown-icon fas fa-window-close fa-fw text-muted"></i> {{ "Cancel"|trans }}</span>
                                                                        {%  endif %}
                                                                        {% if payoutRequest.deletedAt %}
                                                                            <span data-target="{{ path('dashboard_administrator_payout_request_delete', { reference : payoutRequest.reference }) }}" class="dropdown-item requires-confirmation" data-confirmation-text="{{ "You are about to delete the payout request and all its related information"|trans ~ " " ~ "PERMANENTLY"|trans }}"><i class="dropdown-icon fas fa-trash fa-fw text-muted"></i> {{ "Delete permanently"|trans }}</span>
                                                                            <a href="{{ path('dashboard_administrator_payout_request_restore', { reference : payoutRequest.reference }) }}" class="dropdown-item"><i class="dropdown-icon fas fa-trash-restore fa-fw text-muted"></i> {{ "Restore"|trans }}</a>
                                                                        {% else %}
                                                                            <span data-target="{{ path('dashboard_administrator_payout_request_delete', { reference : payoutRequest.reference }) }}" class="dropdown-item requires-confirmation" data-confirmation-text="{{ "You are about to delete the payout request and all its related information"|trans }}"><i class="dropdown-icon fas fa-times fa-fw text-muted"></i> {{ "Delete"|trans }} </span>
                                                                        {% endif %}
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    {% if is_granted("ROLE_ADMINISTRATOR") and payoutRequest.status == 1 or payoutRequest.status == -2 %}

                                                    <div class="modal fade" id="payoutRequest-{{ payoutRequest.reference }}-payment-details">
                                                        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h4 class="modal-title">{{ "Payout request payment details"|trans }} - {{ payoutRequest.reference }}</h4>
                                                                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    {{ payoutRequest.payment|json_encode(constant('JSON_PRETTY_PRINT'))|nl2br }}
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-primary" data-dismiss="modal">{{ "Close"|trans }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                {% endif %}

                                            {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>

                                {% else %}
                                    {% include "Global/message.html.twig" with { type: "info", message: ('No payout requests found'|trans), icon: "fas fa-exclamation-circle" } %}
                                {% endif %}

                            </div>

                            {{ knp_pagination_render(payoutRequests, null, {}, {'align': 'center'}) }}

                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

{% endblock %}

{% block javascripts %}

    <script>

        $(document).ready(function () {

            if (getURLParameter("reference")) {
                $("#reference").val("{{ app.request.get('reference') }}");
            }

            if (getURLParameter("eventdate")) {
                var url = "{{ path('get_eventdate', { reference : "reference" }) }}";
                url = url.replace("reference", getURLParameter("eventdate"));
                $.ajax({
                    type: 'GET',
                    url: url
                }).then(function (data) {
                    $('#fake_eventdate').select2('data', {id: data.id, text: data.text});
                    $('#eventdate').val(data.id).change();
                });

            }

            if (getURLParameter("organizer")) {
                var url = "{{ path('get_organizer', { slug : "slug" }) }}";
                url = url.replace("slug", getURLParameter("organizer"));
                $.ajax({
                    type: 'GET',
                    url: url
                }).then(function (data) {
                    $('#fake_organizer').select2('data', {id: data.slug, text: data.text});
                    $('#organizer').val(data.slug).change();
                });

            }

            if (getURLParameter("datefrom")) {
                $("#datefrom").val("{{ app.request.get('datefrom') }}");
            }

            if (getURLParameter("dateto")) {
                $("#dateto").val("{{ app.request.get('dateto') }}");
            }

            if (getURLParameter('status') != "null") {
                $('input[type="radio"][name="status"][value="' + getURLParameter('status') + '"]').attr("checked", "checked");
            }


        });

    </script>

{% endblock %}