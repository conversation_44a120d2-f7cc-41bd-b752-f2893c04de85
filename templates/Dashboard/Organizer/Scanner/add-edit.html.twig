{% extends "Global/layout.html.twig" %}

{% set pagetitle = 'Add a new scanner' | trans %}

{% if scanner.id %}{% set pagetitle = 'Update the scanner' | trans %}{% endif %}

{% block title %}{{pagetitle}}{% endblock %}

{% block content %}

    {% set navigation = [{ "dashboard_index": ('Dashboard' | trans),  "dashboard_organizer_scanner": ('my scanners' | trans), "current":(pagetitle) }] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <aside class="col-lg-3 pt-3 pt-lg-0">
                    {% include "Dashboard/sidebar.html.twig" %}
                </aside>
                <div class="col-lg-9 mt-4 mt-lg-0">
                    <div class="card box">
                        <div class="card-body">

                            {% if scanner.id %}
                                {% include "Global/message.html.twig" with { type: "info", icon: "fas fa-info-circle", "message": ("Leave the password empty to keep the old one"|trans) } %}
                            {% endif %}

                            {{ form_start(form, {'attr': {'novalidate': 'novalidate'}}) }}
                            {{ form_widget(form) }}
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

{% endblock %}

{% block javascripts %}
    {% if scanner.id %}
        <script>
            $(document).ready(function () {
                $('#scanner_username').val("{{ scanner.user.username }}");
            });
        </script>
    {% endif %}
{% endblock %}