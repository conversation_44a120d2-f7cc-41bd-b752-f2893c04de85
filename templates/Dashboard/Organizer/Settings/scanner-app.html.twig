{% extends "Global/layout.html.twig" %}

{% set pagetitle = 'Scanner App settings' | trans %}

{% block title %}{{pagetitle}}{% endblock %}

{% block content %}

    {% set navigation = [{ "dashboard_index": ('Dashboard' | trans), "current":(pagetitle) }] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <aside class="col-lg-3 pt-3 pt-lg-0">
                    {% include "Dashboard/sidebar.html.twig" %}
                </aside>
                <div class="col-lg-9 mt-4 mt-lg-0">
                    <div class="card box">
                        <div class="card-body">
                            {{ form_start(form, {'attr': {'novalidate': 'novalidate'}, 'name': 'scanner_app_settings'}) }}
                            {{ form_widget(form) }}
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

{% endblock %}