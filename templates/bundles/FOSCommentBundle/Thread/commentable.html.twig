{% trans_default_domain 'FOSCommentBundle' %}

{#

 This file is part of the FOSCommentBundle package.

 (c) FriendsOfSymfony <http://friendsofsymfony.github.com/>

 This source file is subject to the MIT license that is bundled
 with this source code in the file LICENSE.

#}

<div class="fos_comment_commentable_form_holder mt-4 mb-4 text-right">
    <form class="fos_comment_commentable_form" action="{{ url('fos_comment_patch_thread_commentable', {'id': id}) }}" data-fos-comment-thread-id="{{ id }}" method="POST">

        {% block fos_comment_form_fields %}
            {{ form_errors(form) }}

            {{ form_errors(form.isCommentable) }}
            {{ form_widget(form.isCommentable) }}

            {{ form_rest(form) }}
        {% endblock %}

        <div class="fos_comment_submit">
            {% block fos_comment_form_submit %}
                <button type="submit" class="btn btn-sm btn-danger">{{ (isCommentable ? 'fos_comment_thread_open' : 'fos_comment_thread_close') | trans({}, 'FOSCommentBundle') }}</button>
            {% endblock %}
        </div>

    </form>
</div>
