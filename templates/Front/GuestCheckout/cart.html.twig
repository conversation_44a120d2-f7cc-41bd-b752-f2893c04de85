{% extends "Global/layout.html.twig" %}

{% set pagetitle = "Cart"|trans %}

{% block title %}{{ pagetitle }}{% endblock %}

{% block content %}

    {% set navigation = [{ "homepage": ('Home' | trans), "current":(pagetitle) }] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card box">
                        <div class="card-body">
                            <h4 class="card-title mb-4">{{ "Your cart"|trans }}</h4>

                            {% if cartItemsWithDetails is empty %}
                                <div class="alert alert-info">
                                    {{ "Your cart is empty"|trans }}
                                </div>
                                <a href="{{ path('homepage') }}" class="btn btn-primary">
                                    {{ "Browse events"|trans }}
                                </a>
                            {% else %}
                                <form method="post">
                                    {% for item in cartItemsWithDetails %}
                                        <div class="row mb-3 p-3 border rounded">
                                            <div class="col-md-6">
                                                <h6>{{ item.ticket.name }}</h6>
                                                <small class="text-muted">{{ item.ticket.eventdate.event.name }}</small><br>
                                                <small class="text-muted">{{ item.ticket.eventdate.startdate|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}</small>
                                            </div>
                                            <div class="col-md-2">
                                                <strong>{{ item.ticket.getSalePrice|localizedcurrency(services.getSetting("currency_ccy")) }}</strong>
                                            </div>
                                            <div class="col-md-2">
                                                <input type="number" name="{{ item.reference }}" value="{{ item.quantity }}" min="0" class="form-control">
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="this.parentElement.previousElementSibling.querySelector('input').value = 0;">
                                                    {{ "Remove"|trans }}
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}

                                    <div class="mt-4">
                                        <button type="submit" class="btn btn-primary">
                                            {{ "Update cart"|trans }}
                                        </button>
                                        <a href="{{ path('guest_checkout') }}" class="btn btn-success ml-2">
                                            {{ "Proceed to checkout"|trans }}
                                        </a>
                                    </div>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card box">
                        <div class="card-body">
                            <h5>{{ "Guest checkout"|trans }}</h5>
                            <p class="text-muted">
                                {{ "No account required! Just provide your name, email and phone number to complete your purchase."|trans }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

{% endblock %}
