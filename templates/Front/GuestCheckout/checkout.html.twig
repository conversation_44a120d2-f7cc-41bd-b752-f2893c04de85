{% extends "Front/layout.html.twig" %}

{% set pagetitle = "Checkout"|trans %}

{% block title %}{{ pagetitle }}{% endblock %}

{% block content %}

    {% set navigation = [{ "homepage": ('Home' | trans), "guest_cart": ('Cart' | trans), "current":(pagetitle) }] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card box">
                        <div class="card-body">
                            <h4 class="card-title mb-4">{{ "Guest checkout"|trans }}</h4>
                            
                            {{ form_start(form, {'attr': { 'novalidate': 'novalidate' } }) }}
                            
                            <div class="card mt-4 mb-5">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ "Your information"|trans }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="col form-group">
                                            {{ form_label(form.firstname) }}
                                            {{ form_widget(form.firstname) }}
                                            {{ form_errors(form.firstname) }}
                                        </div>
                                        <div class="col form-group">
                                            {{ form_label(form.lastname) }}
                                            {{ form_widget(form.lastname) }}
                                            {{ form_errors(form.lastname) }}
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="col form-group">
                                            {{ form_label(form.email) }}
                                            {{ form_widget(form.email) }}
                                            {{ form_errors(form.email) }}
                                        </div>
                                        <div class="col form-group">
                                            {{ form_label(form.phone) }}
                                            {{ form_widget(form.phone) }}
                                            {{ form_errors(form.phone) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-4 mb-5" style="display: none;">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ "Billing address (optional)"|trans }}</h6>
                                    <small class="text-muted">{{ "You can skip this section for faster checkout"|trans }}</small>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="col form-group">
                                            {{ form_label(form.country, null, {'label_attr': {'class': 'not-required'}}) }}
                                            {{ form_widget(form.country) }}
                                            {{ form_errors(form.country) }}
                                        </div>
                                        <div class="col form-group">
                                            {{ form_label(form.state, null, {'label_attr': {'class': 'not-required'}}) }}
                                            {{ form_widget(form.state) }}
                                            {{ form_errors(form.state) }}
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="col form-group">
                                            {{ form_label(form.city, null, {'label_attr': {'class': 'not-required'}}) }}
                                            {{ form_widget(form.city) }}
                                            {{ form_errors(form.city) }}
                                        </div>
                                        <div class="col form-group">
                                            {{ form_label(form.postalcode, null, {'label_attr': {'class': 'not-required'}}) }}
                                            {{ form_widget(form.postalcode) }}
                                            {{ form_errors(form.postalcode) }}
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="col form-group">
                                            {{ form_label(form.street, null, {'label_attr': {'class': 'not-required'}}) }}
                                            {{ form_widget(form.street) }}
                                            {{ form_errors(form.street) }}
                                        </div>
                                        <div class="col form-group">
                                            {{ form_label(form.street2, null, {'label_attr': {'class': 'not-required'}}) }}
                                            {{ form_widget(form.street2) }}
                                            {{ form_errors(form.street2) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-4 mb-5">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ "Payment method"|trans }}</h6>
                                </div>
                                <div class="card-body">
                                    {% for paymentGateway in paymentGateways %}
                                        {% if paymentGateway.enabled %}
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_gateway" value="{{ paymentGateway.slug }}" id="gateway{{ paymentGateway.id }}" data-paymentGateway-id="{{ paymentGateway.id }}">
                                                <label class="form-check-label" for="gateway{{ paymentGateway.id }}">
                                                    {{ paymentGateway.name }}
                                                </label>
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    {{ "Complete order"|trans }}
                                </button>
                            </div>

                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card box">
                        <div class="card-body">
                            <h5>{{ "Order summary"|trans }}</h5>
                            {% for ticketreference, quantity in cartItems %}
                                <div class="d-flex justify-content-between">
                                    <span>{{ "Ticket"|trans }} #{{ ticketreference }}</span>
                                    <span>{{ quantity }}x</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Show/hide billing address section
        document.addEventListener('DOMContentLoaded', function() {
            const billingCard = document.querySelector('.card:nth-of-type(3)');
            const toggleBtn = document.createElement('button');
            toggleBtn.type = 'button';
            toggleBtn.className = 'btn btn-link btn-sm';
            toggleBtn.textContent = '{{ "Show billing address"|trans }}';
            
            billingCard.parentNode.insertBefore(toggleBtn, billingCard);
            
            toggleBtn.addEventListener('click', function() {
                if (billingCard.style.display === 'none') {
                    billingCard.style.display = 'block';
                    toggleBtn.textContent = '{{ "Hide billing address"|trans }}';
                } else {
                    billingCard.style.display = 'none';
                    toggleBtn.textContent = '{{ "Show billing address"|trans }}';
                }
            });
        });
    </script>

{% endblock %}
