{% extends "Front/layout.html.twig" %}

{% set pagetitle = "Order confirmation"|trans %}

{% block title %}{{ pagetitle }}{% endblock %}

{% block content %}

    {% set navigation = [{ "homepage": ('Home' | trans), "current":(pagetitle) }] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card box">
                        <div class="card-body text-center">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            
                            <h2 class="text-success">{{ "Order confirmed!"|trans }}</h2>
                            <p class="lead">{{ "Thank you for your purchase"|trans }}</p>
                            
                            <div class="alert alert-info">
                                <h5>{{ "Order"|trans }} #{{ order.reference }}</h5>
                                <p class="mb-0">
                                    {{ "You will receive a confirmation email with your tickets shortly."|trans }}
                                </p>
                            </div>
                            
                            <div class="mt-4">
                                <a href="{{ path('homepage') }}" class="btn btn-primary">
                                    {{ "Back to events"|trans }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

{% endblock %}
