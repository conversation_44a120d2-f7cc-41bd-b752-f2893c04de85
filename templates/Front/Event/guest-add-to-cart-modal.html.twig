<div class="modal fade {% if eventdate.hasSeatingPlan %}has-seating-plan{% endif %}" id="buy-tickets-modal-eventDate-{{ eventdate.reference }}" style="z-index: 1111111;">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    {% if eventdate.hasSeatingPlan %}
                        <i class="fas fa-braille"></i> {{ "Pick your seats"|trans }}
                    {% else %}
                        <i class="fas fa-ticket-alt"></i> {{ "Tickets"|trans }}
                    {% endif %}
                </h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <h6 class="b">{{ eventdate.event.name }}</h6>
                <p class="text-sm mb-1 text-muted"><i class="fas fa-clock fa-fw"></i> {{ eventdate.startdate|localizeddate('none', 'none', app.request.locale, date_timezone, date_format) }}</p>

                <p class="text-sm text-muted"><i class="fas fa-map-marker-alt fa-fw"></i>
                    {% if eventdate.venue %}
                        {{ eventdate.venue.name }}: {{ eventdate.venue.stringifyAddress }}
                    {% else %}
                        {{ "Online"|trans }}
                    {% endif %}
                </p>

                {% if eventdate.hasSeatingPlan %}
                    <div class="mb-3">
                        {% include "Dashboard/Shared/Venue/SeatingPlans/seating-plan-seats.html.twig" with {seatingPlan: eventdate.seatingPlan} %}
                    </div>
                {% endif %}

                <div class="table-responsive">
                    <table class="table table-hover table-condensed mb-0">
                        <tbody>
                            {% for ticket in eventdate.tickets %}
                                {% if ticket.active %}
                                    <tr class="bg-gray">
                                        <td class="border-top-white">
                                            <div>
                                                {{ ticket.name }}
                                                {% if ticket.description %}
                                                    <i class="fas fa-info-circle text-primary ml-2" data-toggle="tooltip" title="{{ ticket.description }}"></i>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="border-top-white text-right">
                                            {% if ticket.free %}
                                                <span class="badge badge-success">{{ "Free"|trans }}</span>
                                            {% else %}
                                                <span class="h6 text-primary">{{ ticket.getSalePrice|localizedcurrency(services.getSetting("currency_ccy")) }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="border-top-white text-right">
                                            {% if ticket.isOnSale %}
                                                <input type="text" class="form-control touchspin-integer bg-white eventdate-ticket-qte guest-ticket-qte" name="{{ ticket.reference }}" data-ticket-id="{{ ticket.id }}" data-min="0" data-max="{{ ticket.getMaxTicketsForSaleCount }}" />
                                                {% if services.getSetting("show_tickets_left_on_cart_modal") == "yes" %}
                                                    <span class="badge badge-info mt-2">{{ ticket.getTicketsLeftCount }} {{ "tickets left"|trans }}</span>
                                                {% endif %}
                                                {% if ticket.ticketsperattendee %}
                                                    <span class="badge badge-info ml-3 mt-2">{{ ticket.ticketsperattendee }} {{ "tickets per attendee"|trans }}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge badge-secondary">{{ ticket.stringifyStatus }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" data-dismiss="modal"><i class="far fa-window-close"></i> {{ "Close"|trans }}</button>
                {% if not eventdate.event.hasTwoOrMoreEventDatesOnSale %}
                    <button id="guest-add-to-cart-button" type="button" class="btn btn-primary {% if eventdate.hasSeatingPlan %}event-date-has-seating-plan{% endif %}" data-eventdate-ref="{{ eventdate.reference }}"><i class="fas fa-cart-plus"></i> {{ "Add to cart"|trans }}</button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle guest add to cart
    document.getElementById('guest-add-to-cart-button')?.addEventListener('click', function(e) {
        e.preventDefault();
        
        var ticketsData = {};
        var totalTickets = 0;
        
        // Collect ticket quantities
        document.querySelectorAll('.guest-ticket-qte').forEach(function(input) {
            var quantity = parseInt(input.value) || 0;
            if (quantity > 0) {
                ticketsData[input.name] = quantity;
                totalTickets += quantity;
            }
        });
        
        if (totalTickets === 0) {
            alert('{{ "Please select the tickets quantity you want to buy"|trans }}');
            return;
        }
        
        // Send to guest cart
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ path("guest_cart_add_multiple") }}';
        
        // Add CSRF token if available
        var csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            var csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }
        
        // Add ticket data
        for (var ticketRef in ticketsData) {
            var input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'tickets[' + ticketRef + ']';
            input.value = ticketsData[ticketRef];
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
    });
});
</script>
