{% extends "Global/layout.html.twig" %}

{% set pagetitle = 'Support for'|trans ~ " " ~ category.name %}

{% block title %}{{pagetitle}}{% endblock %}

{% block content %}

    {% set navigation = [{"help_center": ("Help center"|trans), "current" : (pagetitle)}] %}
    {% include "Global/navigation.html.twig" with navigation %}

    <section class="section-content padding-y bg-white">
        <div class="{{ services.getSetting("app_layout") }}">
            <div class="row">
                <div class="col-sm-12">
                    <div class="card box">
                        <div class="card-body">
                            <article class="h-100">
                                <figure class="itembox text-center">
                                    <span class="mt-2 icon-wrap rounded icon-lg bg-primary"><i class="{{ category.icon }}  white"></i></span>
                                    <figcaption class="text-wrap">
                                        <h5 class="title">{{ category.name }}</h5>
                                    </figcaption>
                                </figure>

                                <hr>

                                {% for article in services.getHelpCenterArticles({"category":category.slug}).getQuery().getResult() %}
                                    <p><a href="{{ path('help_center_article', {slug: article.slug}) }}">{{ article.title }}</a></p>
                                    {% endfor %}
                            </article>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

{% endblock %}