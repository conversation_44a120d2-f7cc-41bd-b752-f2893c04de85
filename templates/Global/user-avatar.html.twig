{% if user and user.hasRole('ROLE_ATTENDEE') %}
    <span class="avatar"{% if user.avatarName %}  style="background: url('{{ asset(user.getAvatarPath) }}');"{% elseif user.facebookProfilePicture %} style="background: url('{{ user.facebookProfilePicture }}');"{% endif %} {% if showusernametooltip is defined %}data-toggle="tooltip" title="{{ user.username }}" data-container="body"{% endif %} >
        {% if not user.avatarName and not user.facebookProfilePicture %}
            <i class="far fa-user"></i>
        {% endif %}
    </span>
{% elseif user and user.hasRole('ROLE_ORGANIZER') %}
    <span class="avatar"{% if user.organizer.logoName %} style="background: url('{{ asset(user.organizer.getLogoPath) }}');"{% endif %} {% if showusernametooltip is defined %}data-toggle="tooltip" title="{{ user.username }}" data-container="body"{% endif %} >
        {% if not user.organizer.logoName %}
            <i class="far fa-user"></i>
        {% endif %}
    </span>
{% elseif user and user.hasRole('ROLE_POINTOFSALE') %}
    <span class="avatar"{% if user.pointofsale.organizer.logoName %} style="background: url('{{ asset(user.pointofsale.organizer.getLogoPath) }}');"{% endif %} {% if showusernametooltip is defined %}data-toggle="tooltip" title="{{ user.username }}" data-container="body"{% endif %} >
        {% if not user.pointofsale.organizer.logoName %}
            <i class="far fa-user"></i>
        {% endif %}
    </span>
{% elseif user and user.hasRole('ROLE_SCANNER') %}
    <span class="avatar"{% if user.scanner.organizer.logoName %} style="background: url('{{ asset(user.scanner.organizer.getLogoPath) }}');"{% endif %} {% if showusernametooltip is defined %}data-toggle="tooltip" title="{{ user.username }}" data-container="body"{% endif %} >
        {% if not user.scanner.organizer.logoName %}
            <i class="far fa-user"></i>
        {% endif %}
    </span>
{% else %}
    <span class="avatar">
        <i class="far fa-user"></i>
    </span>
{% endif %}
