{% if pageCount > 1 %}
    <nav class="pt-3">
        {% set classAlign = (align is not defined) ? '' : align=='center' ? ' justify-content-center' : (align=='right' ? ' justify-content-end' : '') %}
        {% set classSize = (size is not defined) ? '' : size=='large' ? ' pagination-lg' : (size=='small' ? ' pagination-sm' : '') %}
        <ul class="pagination{{ classAlign }}{{ classSize }}">

            {% if previous is defined %}
                <li class="page-item">
                    <a class="page-link" rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}">
                        {% if app.request.locale == 'ar' %}
                            <i class="fas fa-angle-right"></i>
                        {% else %}
                            <i class="fas fa-angle-left"></i>
                        {% endif %}
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">
                        {% if app.request.locale == 'ar' %}
                            <i class="fas fa-angle-right"></i>
                        {% else %}
                            <i class="fas fa-angle-left"></i>
                        {% endif %}
                    </span>
                </li>
            {% endif %}

            {% if startPage > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): 1})) }}">1</a>
                </li>
                {% if startPage == 3 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): 2})) }}">2</a>
                    </li>
                {% elseif startPage != 2 %}
                    <li class="page-item disabled">
                        <span class="page-link">&hellip;</span>
                    </li>
                {% endif %}
            {% endif %}

            {% for page in pagesInRange %}
                {% if page != current %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): page})) }}">{{ page }}</a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                {% endif %}

            {% endfor %}

            {% if pageCount > endPage %}
                {% if pageCount > (endPage + 1) %}
                    {% if pageCount > (endPage + 2) %}
                        <li class="page-item disabled">
                            <span class="page-link">&hellip;</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): (pageCount - 1)})) }}">{{ pageCount -1 }}</a>
                        </li>
                    {% endif %}
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): pageCount})) }}">{{ pageCount }}</a>
                </li>
            {% endif %}

            {% if next is defined %}
                <li class="page-item">
                    <a class="page-link" rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}">
                        {% if app.request.locale == 'ar' %}
                            <i class="fas fa-angle-left"></i>
                        {% else %}
                            <i class="fas fa-angle-right"></i>
                        {% endif %}
                    </a>
                </li>
            {% else %}
                <li  class="page-item disabled">
                    <span class="page-link">
                        {% if app.request.locale == 'ar' %}
                            <i class="fas fa-angle-left"></i>
                        {% else %}
                            <i class="fas fa-angle-right"></i>
                        {% endif %}
                    </span>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
