webpack_encore:
    # The path where Encore is building the assets.
    # This should match Encore.setOutputPath() in webpack.config.js.
    output_path: '%kernel.project_dir%/public/assets/'
    # If multiple builds are defined (as shown below), you can disable the default build:
    # output_path: false

    # if using Encore.enableIntegrityHashes() specify the crossorigin attribute value (default: false, or use 'anonymous' or 'use-credentials')
    # crossorigin: 'anonymous'

    # Cache the entrypoints.json (rebuild Symfony's cache when entrypoints.json changes)
    # Available in version 1.2
    #cache: '%kernel.debug%'
