security:
    encoders:
        App\Entity\User: bcrypt
        Symfony\Component\Security\Core\User\User: bcrypt

    role_hierarchy:
        ROLE_ADMIN:       ROLE_USER
        ROLE_SUPER_ADMIN: [ROLE_ADMIN, ROLE_ALLOWED_TO_SWITCH]
        ROLE_ADMINISTRATOR: ROLE_SUPER_ADMIN

    providers:
        chain_provider:
            chain:
                providers:
                    [in_memory, fos_userbundle]
        fos_userbundle:
            id: fos_user.user_provider.username_email
        in_memory:
            memory:
                users:
        scanner_api_provider:
            entity:
                class: App\Entity\User
                property: apiKey
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        scanner_api:
            logout: ~
            provider: scanner_api_provider
            pattern: ^(.*?)/api/scanner/
            guard:
                authenticators:
                    - App\Security\TokenAuthenticator
        main:
            provider: chain_provider
            pattern: ^/
            form_login:
                login_path: fos_user_security_login
                check_path: fos_user_security_check
                default_target_path: dashboard_index
                csrf_token_generator: security.csrf.token_manager
                remember_me: true
            remember_me:
                secret:   '%kernel.secret%'
                lifetime: 2592000 # 1 month in seconds
                path: /
                domain: ~
            logout:
                path:   fos_user_security_logout
            anonymous:    true
            oauth:
                resource_owners:
                    facebook:           "/login/check-facebook"
                    google:             "/login/check-google"
                login_path:        fos_user_security_login
                failure_path:      fos_user_security_login

                oauth_user_provider:
                    service: hwi_oauth_user_provider
            switch_user: true
            access_denied_handler: App\Security\AccessDeniedHandler

    access_control:
        - { path: ^(.*?)/signin$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^(.*?)/signup/attendee, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^(.*?)/signup/organizer, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^(.*?)/resetting, role: IS_AUTHENTICATED_ANONYMOUSLY }
#        - { path: ^(.*?)/dashboard, role: IS_AUTHENTICATED_REMEMBERED }
        - { path: ^(.*?)/dashboard/administrator, role: ROLE_ADMINISTRATOR }
        - { path: ^(.*?)/_trans/, role: ROLE_ADMINISTRATOR }
        - { path: ^(.*?)/_console, role: ROLE_ADMINISTRATOR }
        - { path: ^(.*?)/dashboard/organizer, role: ROLE_ORGANIZER }
        - { path: ^(.*?)/dashboard/attendee, role: ROLE_ATTENDEE }
        - { path: ^(.*?)/dashboard/scanner, role: ROLE_SCANNER }
        - { path: ^(.*?)/dashboard/pointofsale, role: ROLE_POINTOFSALE }