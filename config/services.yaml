# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    is_website_configured: '%env(IS_WEBSITE_CONFIGURED)%'
    maintenance_mode: '%env(MAINTENANCE_MODE)%'
    # The above parameters are declared here to be catched by the related event subscriber
    date_format: '%env(DATE_FORMAT)%'
    date_format_simple: '%env(DATE_FORMAT_SIMPLE)%'
    date_format_date_only: '%env(DATE_FORMAT_DATE_ONLY)%'
    date_timezone: '%env(DATE_TIMEZONE)%'
    locale: '%env(DEFAULT_LOCALE)%'
    locales: '%env(APP_LOCALES)%'
    available_locales: en|fr|es|ar|de|pt|it|br|
    website_name: '%env(WEBSITE_NAME)%'
    no_reply_email: '%env(NO_REPLY_EMAIL)%'
    website_root_url: '%env(WEBSITE_ROOT_URL)%'
    google_maps_api_key: '%env(GOOGLE_MAPS_API_KEY)%'
    google_recaptcha_site_key: '%env(GOOGLE_RECAPTCHA_SITE_KEY)%'
    hwi_oauth_user_provider.class: App\Security\FOSUBUserProvider
    router.request_context.host: "%website_root_url%"
    #router.request_context.base_url: "%website_root_url%"
    #asset.request_context.base_path: '%router.request_context.base_url%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        public: false       # Allows optimizing the container by removing unused services; this also means
                            # fetching services directly from the container via $container->get() won't work.
                            # The best practice is to be explicit about your dependencies anyway.
    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    JMS\TranslationBundle\Controller\TranslateController:
        public: true
        arguments:
            $configFactory: '@jms_translation.config_factory'
            $loader: '@jms_translation.loader_manager'


    JMS\TranslationBundle\Controller\ApiController:
        public: true
        arguments:
            $configFactory: '@jms_translation.config_factory'
            $updater: '@jms_translation.updater'

    stof_doctrine_extensions.listener.sluggable:
        class: "%stof_doctrine_extensions.listener.sluggable.class%"
        public: false
        tags:
            - { name: doctrine.event_subscriber, connection: default }
        calls:
            - [setAnnotationReader, ['@annotation_reader']]
            - [setTransliterator, [[App\Service\Slugger, transliterate]]]
            - [setUrlizer, [[App\Service\Slugger, urlize]]]

    app.form.registration:
        class: App\Form\RegistrationType
        tags:
            - { name: form.type, alias: app_user_registration }

    App\Controller\bundles\FOSUserBundle\RegistrationController:
        arguments:
            $formFactory: '@fos_user.registration.form.factory'

    App\Controller\bundles\FOSUserBundle\ChangePasswordController:
        arguments:
            $formFactory: '@fos_user.change_password.form.factory'

    App\Controller\bundles\FOSUserBundle\ResettingController:
        arguments:
            $formFactory: '@fos_user.resetting.form.factory'
            $mailer: '@fos_user.mailer.default'
            $retryTtl: null

    hwi_oauth_user_provider:
        class: "%hwi_oauth_user_provider.class%"
        arguments: ["@fos_user.user_manager",{facebook: facebook_id, google: google_id}]

    twig.extension.intl:
        class: Twig\Extensions\IntlExtension
        tags:
            - { name: twig.extension }

    app.services:
        alias: App\Service\AppServices

    debril.rss_atom.provider:
        class: App\Feed\Provider
        arguments: ["@app.services", "@router.default", "@assets.packages"]

    DrewM\MailChimp\MailChimp: '@welp_mailchimp.mailchimp_master'

    eventic_mailchimp_fos_subscriber_provider:
        class: Welp\MailchimpBundle\Provider\FosSubscriberProvider
        arguments: ["@fos_user.user_manager"]

    form.extension.type.gateway_config.crypted:
        class: App\Form\Extension\CryptedGatewayConfigTypeExtension
        arguments:
            $cypher: '@payum.dynamic_gateways.cypher'
        tags:
            - { name: form.type_extension, extended-type: App\Form\PaymentGatewayConfigType }

    App\Controller\Dashboard\Shared\PayoutRequestController:
        class: App\Controller\Dashboard\Shared\PayoutRequestController
        arguments:
            $cypher: '@payum.dynamic_gateways.cypher'

    coresphere_console.controller:
        class: CoreSphere\ConsoleBundle\Controller\ConsoleController
        arguments:
            - "@templating"
            - "@coresphere_console.executer"
            - "@coresphere_console.application"
            - "%kernel.environment%"
        tags:
            - { name: controller.service_arguments }

    sitemap.subscriber:
        class: App\EventListener\SitemapSubscriber
        arguments:
            - "@router"
            - "@app.services"
        tags:
            - { name: "kernel.event_subscriber", priority: 100 }

    App\Controller\Front\FlutterwaveController:
        class: App\Controller\Front\FlutterwaveController
        arguments:
            $cypher: '@payum.dynamic_gateways.cypher'

    App\Controller\Front\MercadoPagoController:
        class: App\Controller\Front\MercadoPagoController
        arguments:
            $cypher: '@payum.dynamic_gateways.cypher'

    App\Controller\Front\PesaPalController:
        class: App\Controller\Front\PesaPalController
        arguments:
            $cypher: '@payum.dynamic_gateways.cypher'

    App\Controller\Front\GuestCheckoutController:
        class: App\Controller\Front\GuestCheckoutController
        arguments:
            $payum: '@payum'