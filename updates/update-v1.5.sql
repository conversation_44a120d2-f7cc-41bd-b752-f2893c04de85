CREATE TABLE eventic_seating_plan (id INT AUTO_INCREMENT NOT NULL, venue_id INT DEFAULT NULL, design JSON DEFAULT NULL, updated_at DATETIME DEFAULT NULL, INDEX IDX_9858097940A73EBA (venue_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE eventic_seating_plan_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(50) NOT NULL, slug VARCHAR(80) NOT NULL, locale VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_F3F454EB989D9B62 (slug), INDEX IDX_F3F454EB2C2AC5D3 (translatable_id), UNIQUE INDEX eventic_seating_plan_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE eventic_seating_plan ADD CONSTRAINT FK_9858097940A73EBA FOREIGN KEY (venue_id) REFERENCES eventic_venue (id);
ALTER TABLE eventic_seating_plan_translation ADD CONSTRAINT FK_F3F454EB2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES eventic_seating_plan (id) ON DELETE CASCADE;
ALTER TABLE eventic_amenity CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_amenity_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_app_layout_setting CHANGE logo_name logo_name VARCHAR(50) DEFAULT NULL, CHANGE logo_size logo_size INT DEFAULT NULL, CHANGE logo_mime_type logo_mime_type VARCHAR(50) DEFAULT NULL, CHANGE logo_original_name logo_original_name VARCHAR(1000) DEFAULT NULL, CHANGE logo_dimensions logo_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE favicon_name favicon_name VARCHAR(50) DEFAULT NULL, CHANGE favicon_size favicon_size INT DEFAULT NULL, CHANGE favicon_mime_type favicon_mime_type VARCHAR(50) DEFAULT NULL, CHANGE favicon_original_name favicon_original_name VARCHAR(1000) DEFAULT NULL, CHANGE favicon_dimensions favicon_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE og_image_name og_image_name VARCHAR(50) DEFAULT NULL, CHANGE og_image_size og_image_size INT DEFAULT NULL, CHANGE og_image_mime_type og_image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE og_image_original_name og_image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE og_image_dimensions og_image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)';
ALTER TABLE eventic_audience CHANGE image_name image_name VARCHAR(50) DEFAULT NULL, CHANGE image_size image_size INT DEFAULT NULL, CHANGE image_mime_type image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE image_original_name image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE image_dimensions image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_audience_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_blog_post CHANGE category_id category_id INT DEFAULT NULL, CHANGE readtime readtime INT DEFAULT NULL, CHANGE image_name image_name VARCHAR(50) DEFAULT NULL, CHANGE image_size image_size INT DEFAULT NULL, CHANGE image_mime_type image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE image_original_name image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE image_dimensions image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE views views INT DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_blog_post_category CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_blog_post_category_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_blog_post_translation CHANGE translatable_id translatable_id INT DEFAULT NULL, CHANGE tags tags VARCHAR(500) DEFAULT NULL;
ALTER TABLE eventic_cart_element ADD reserved_seats JSON DEFAULT NULL, CHANGE user_id user_id INT DEFAULT NULL, CHANGE eventticket_id eventticket_id INT DEFAULT NULL, CHANGE quantity quantity INT DEFAULT NULL, CHANGE ticket_fee ticket_fee NUMERIC(10, 2) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_category CHANGE image_name image_name VARCHAR(50) DEFAULT NULL, CHANGE image_size image_size INT DEFAULT NULL, CHANGE image_mime_type image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE image_original_name image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE image_dimensions image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE featuredorder featuredorder INT DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_category_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_comment CHANGE thread_id thread_id VARCHAR(255) DEFAULT NULL, CHANGE author_id author_id INT DEFAULT NULL;
ALTER TABLE eventic_country CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_country_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_currency CHANGE symbol symbol VARCHAR(50) DEFAULT NULL;
ALTER TABLE eventic_event CHANGE category_id category_id INT DEFAULT NULL, CHANGE country_id country_id INT DEFAULT NULL, CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE isonhomepageslider_id isonhomepageslider_id INT DEFAULT NULL, CHANGE youtubeurl youtubeurl VARCHAR(255) DEFAULT NULL, CHANGE externallink externallink VARCHAR(255) DEFAULT NULL, CHANGE phonenumber phonenumber VARCHAR(50) DEFAULT NULL, CHANGE email email VARCHAR(255) DEFAULT NULL, CHANGE twitter twitter VARCHAR(255) DEFAULT NULL, CHANGE instagram instagram VARCHAR(255) DEFAULT NULL, CHANGE facebook facebook VARCHAR(255) DEFAULT NULL, CHANGE googleplus googleplus VARCHAR(255) DEFAULT NULL, CHANGE linkedin linkedin VARCHAR(255) DEFAULT NULL, CHANGE artists artists VARCHAR(500) DEFAULT NULL, CHANGE tags tags VARCHAR(500) DEFAULT NULL, CHANGE year year VARCHAR(5) DEFAULT NULL, CHANGE image_name image_name VARCHAR(50) DEFAULT NULL, CHANGE image_size image_size INT DEFAULT NULL, CHANGE image_mime_type image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE image_original_name image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE image_dimensions image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_event_date ADD seating_plan_id INT DEFAULT NULL, ADD has_seating_plan TINYINT(1) DEFAULT NULL, CHANGE event_id event_id INT DEFAULT NULL, CHANGE venue_id venue_id INT DEFAULT NULL, CHANGE startdate startdate DATETIME DEFAULT NULL, CHANGE enddate enddate DATETIME DEFAULT NULL;
ALTER TABLE eventic_event_date ADD CONSTRAINT FK_D30F7AD319E3A7BA FOREIGN KEY (seating_plan_id) REFERENCES eventic_seating_plan (id);
CREATE INDEX IDX_D30F7AD319E3A7BA ON eventic_event_date (seating_plan_id);
ALTER TABLE eventic_event_image CHANGE event_id event_id INT DEFAULT NULL, CHANGE image_name image_name VARCHAR(50) DEFAULT NULL, CHANGE image_size image_size INT DEFAULT NULL, CHANGE image_mime_type image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE image_original_name image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE image_dimensions image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE position position INT DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_event_date_ticket ADD seating_plan_sections LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE eventdate_id eventdate_id INT DEFAULT NULL, CHANGE description description VARCHAR(255) DEFAULT NULL, CHANGE price price NUMERIC(10, 2) DEFAULT NULL, CHANGE promotionalprice promotionalprice NUMERIC(10, 2) DEFAULT NULL, CHANGE quantity quantity INT DEFAULT NULL, CHANGE ticketsperattendee ticketsperattendee INT DEFAULT NULL, CHANGE salesstartdate salesstartdate DATETIME DEFAULT NULL, CHANGE salesenddate salesenddate DATETIME DEFAULT NULL;
ALTER TABLE eventic_event_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_help_center_article CHANGE category_id category_id INT DEFAULT NULL, CHANGE views views INT DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_help_center_article_translation CHANGE translatable_id translatable_id INT DEFAULT NULL, CHANGE tags tags VARCHAR(150) DEFAULT NULL;
ALTER TABLE eventic_help_center_category CHANGE parent_id parent_id INT DEFAULT NULL, CHANGE icon icon VARCHAR(50) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_help_center_category_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_homepage_hero_setting CHANGE custom_background_name custom_background_name VARCHAR(50) DEFAULT NULL, CHANGE custom_background_size custom_background_size INT DEFAULT NULL, CHANGE custom_background_mime_type custom_background_mime_type VARCHAR(50) DEFAULT NULL, CHANGE custom_background_original_name custom_background_original_name VARCHAR(1000) DEFAULT NULL, CHANGE custom_background_dimensions custom_background_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE show_search_box show_search_box TINYINT(1) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_homepage_hero_setting_translation CHANGE translatable_id translatable_id INT DEFAULT NULL, CHANGE title title VARCHAR(100) DEFAULT NULL, CHANGE paragraph paragraph VARCHAR(500) DEFAULT NULL;
ALTER TABLE eventic_language CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_language_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_menu_element CHANGE menu_id menu_id INT DEFAULT NULL, CHANGE icon icon VARCHAR(50) DEFAULT NULL, CHANGE link link VARCHAR(255) DEFAULT NULL, CHANGE custom_link custom_link VARCHAR(255) DEFAULT NULL;
ALTER TABLE eventic_menu_element_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_menu_translation CHANGE translatable_id translatable_id INT DEFAULT NULL, CHANGE header header VARCHAR(50) DEFAULT NULL;
ALTER TABLE eventic_order CHANGE user_id user_id INT DEFAULT NULL, CHANGE paymentgateway_id paymentgateway_id INT DEFAULT NULL, CHANGE payment_id payment_id INT DEFAULT NULL, CHANGE note note VARCHAR(1000) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_order_element ADD reserved_seats JSON DEFAULT NULL, CHANGE order_id order_id INT DEFAULT NULL, CHANGE eventticket_id eventticket_id INT DEFAULT NULL, CHANGE unitprice unitprice NUMERIC(10, 2) DEFAULT NULL, CHANGE quantity quantity INT DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_order_ticket ADD reserved_seat JSON DEFAULT NULL, CHANGE orderelement_id orderelement_id INT DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_organizer CHANGE user_id user_id INT DEFAULT NULL, CHANGE country_id country_id INT DEFAULT NULL, CHANGE description description VARCHAR(1000) DEFAULT NULL, CHANGE website website VARCHAR(50) DEFAULT NULL, CHANGE email email VARCHAR(50) DEFAULT NULL, CHANGE phone phone VARCHAR(50) DEFAULT NULL, CHANGE facebook facebook VARCHAR(100) DEFAULT NULL, CHANGE twitter twitter VARCHAR(100) DEFAULT NULL, CHANGE instagram instagram VARCHAR(100) DEFAULT NULL, CHANGE googleplus googleplus VARCHAR(100) DEFAULT NULL, CHANGE linkedin linkedin VARCHAR(100) DEFAULT NULL, CHANGE youtubeurl youtubeurl VARCHAR(255) DEFAULT NULL, CHANGE logo_name logo_name VARCHAR(50) DEFAULT NULL, CHANGE logo_size logo_size INT DEFAULT NULL, CHANGE logo_mime_type logo_mime_type VARCHAR(50) DEFAULT NULL, CHANGE logo_original_name logo_original_name VARCHAR(1000) DEFAULT NULL, CHANGE logo_dimensions logo_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE cover_name cover_name VARCHAR(50) DEFAULT NULL, CHANGE cover_size cover_size INT DEFAULT NULL, CHANGE cover_mime_type cover_mime_type VARCHAR(50) DEFAULT NULL, CHANGE cover_original_name cover_original_name VARCHAR(1000) DEFAULT NULL, CHANGE cover_dimensions cover_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL, CHANGE show_event_date_stats_on_scanner_app show_event_date_stats_on_scanner_app TINYINT(1) DEFAULT NULL, CHANGE allow_tap_to_check_in_on_scanner_app allow_tap_to_check_in_on_scanner_app TINYINT(1) DEFAULT NULL;
ALTER TABLE eventic_page CHANGE updated_at updated_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_page_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_payment CHANGE order_id order_id INT DEFAULT NULL, CHANGE country_id country_id INT DEFAULT NULL, CHANGE number number VARCHAR(255) DEFAULT NULL, CHANGE description description VARCHAR(255) DEFAULT NULL, CHANGE client_email client_email VARCHAR(255) DEFAULT NULL, CHANGE client_id client_id VARCHAR(255) DEFAULT NULL, CHANGE total_amount total_amount INT DEFAULT NULL, CHANGE currency_code currency_code VARCHAR(255) DEFAULT NULL, CHANGE firstname firstname VARCHAR(20) DEFAULT NULL, CHANGE lastname lastname VARCHAR(20) DEFAULT NULL, CHANGE state state VARCHAR(50) DEFAULT NULL, CHANGE city city VARCHAR(50) DEFAULT NULL, CHANGE postalcode postalcode VARCHAR(50) DEFAULT NULL, CHANGE street street VARCHAR(50) DEFAULT NULL, CHANGE street2 street2 VARCHAR(50) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_payment_gateway CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE gateway_logo_name gateway_logo_name VARCHAR(255) DEFAULT NULL, CHANGE number number INT DEFAULT NULL;
ALTER TABLE eventic_payment_gateway_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_payment_token CHANGE details details LONGTEXT DEFAULT NULL COMMENT '(DC2Type:object)';
ALTER TABLE eventic_payout_request CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE payment_gateway_id payment_gateway_id INT DEFAULT NULL, CHANGE event_date_id event_date_id INT DEFAULT NULL, CHANGE payment payment JSON DEFAULT NULL COMMENT '(DC2Type:json_array)', CHANGE note note VARCHAR(1000) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_pointofsale CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE user_id user_id INT DEFAULT NULL;
ALTER TABLE eventic_review CHANGE event_id event_id INT DEFAULT NULL, CHANGE user_id user_id INT DEFAULT NULL, CHANGE headline headline VARCHAR(100) DEFAULT NULL, CHANGE details details VARCHAR(500) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_scanner CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE user_id user_id INT DEFAULT NULL;
ALTER TABLE eventic_settings CHANGE value value VARCHAR(4000) DEFAULT NULL;
ALTER TABLE eventic_thread CHANGE last_comment_at last_comment_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_ticket_reservation CHANGE eventticket_id eventticket_id INT DEFAULT NULL, CHANGE user_id user_id INT DEFAULT NULL, CHANGE orderelement_id orderelement_id INT DEFAULT NULL, CHANGE quantity quantity INT DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_user CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE scanner_id scanner_id INT DEFAULT NULL, CHANGE pointofsale_id pointofsale_id INT DEFAULT NULL, CHANGE isorganizeronhomepageslider_id isorganizeronhomepageslider_id INT DEFAULT NULL, CHANGE country_id country_id INT DEFAULT NULL, CHANGE salt salt VARCHAR(255) DEFAULT NULL, CHANGE last_login last_login DATETIME DEFAULT NULL, CHANGE confirmation_token confirmation_token VARCHAR(180) DEFAULT NULL, CHANGE password_requested_at password_requested_at DATETIME DEFAULT NULL, CHANGE gender gender VARCHAR(10) DEFAULT NULL, CHANGE firstname firstname VARCHAR(20) DEFAULT NULL, CHANGE lastname lastname VARCHAR(20) DEFAULT NULL, CHANGE street street VARCHAR(50) DEFAULT NULL, CHANGE street2 street2 VARCHAR(50) DEFAULT NULL, CHANGE city city VARCHAR(50) DEFAULT NULL, CHANGE state state VARCHAR(50) DEFAULT NULL, CHANGE postalcode postalcode VARCHAR(15) DEFAULT NULL, CHANGE phone phone VARCHAR(50) DEFAULT NULL, CHANGE birthdate birthdate DATE DEFAULT NULL, CHANGE avatar_name avatar_name VARCHAR(50) DEFAULT NULL, CHANGE avatar_size avatar_size INT DEFAULT NULL, CHANGE avatar_mime_type avatar_mime_type VARCHAR(50) DEFAULT NULL, CHANGE avatar_original_name avatar_original_name VARCHAR(1000) DEFAULT NULL, CHANGE avatar_dimensions avatar_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL, CHANGE facebook_id facebook_id VARCHAR(255) DEFAULT NULL, CHANGE facebook_access_token facebook_access_token VARCHAR(255) DEFAULT NULL, CHANGE google_id google_id VARCHAR(255) DEFAULT NULL, CHANGE google_access_token google_access_token VARCHAR(255) DEFAULT NULL, CHANGE api_key api_key VARCHAR(255) DEFAULT NULL, CHANGE facebook_profile_picture facebook_profile_picture VARCHAR(1000) DEFAULT NULL;
ALTER TABLE eventic_venue CHANGE organizer_id organizer_id INT DEFAULT NULL, CHANGE type_id type_id INT DEFAULT NULL, CHANGE country_id country_id INT DEFAULT NULL, CHANGE seatedguests seatedguests INT DEFAULT NULL, CHANGE standingguests standingguests INT DEFAULT NULL, CHANGE neighborhoods neighborhoods VARCHAR(100) DEFAULT NULL, CHANGE foodbeverage foodbeverage VARCHAR(500) DEFAULT NULL, CHANGE pricing pricing VARCHAR(500) DEFAULT NULL, CHANGE availibility availibility VARCHAR(500) DEFAULT NULL, CHANGE street2 street2 VARCHAR(50) DEFAULT NULL, CHANGE lat lat VARCHAR(255) DEFAULT NULL, CHANGE lng lng VARCHAR(255) DEFAULT NULL, CHANGE contactemail contactemail VARCHAR(50) DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_venue_image CHANGE venue_id venue_id INT DEFAULT NULL, CHANGE image_name image_name VARCHAR(50) DEFAULT NULL, CHANGE image_size image_size INT DEFAULT NULL, CHANGE image_mime_type image_mime_type VARCHAR(50) DEFAULT NULL, CHANGE image_original_name image_original_name VARCHAR(1000) DEFAULT NULL, CHANGE image_dimensions image_dimensions LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', CHANGE position position INT DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_venue_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_venue_type CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE deleted_at deleted_at DATETIME DEFAULT NULL;
ALTER TABLE eventic_venue_type_translation CHANGE translatable_id translatable_id INT DEFAULT NULL;
ALTER TABLE eventic_vote CHANGE comment_id comment_id INT DEFAULT NULL, CHANGE voter_id voter_id INT DEFAULT NULL;